#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征工程优化脚本
分析当前特征的有效性，实现特征选择和降维
"""

import sys
import os
import numpy as np
import cv2
import json
import pickle
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score
import matplotlib.pyplot as plt
import seaborn as sns

sys.path.append('.')
from custom_workpiece_learner import CustomWorkpieceLearner

class FeatureOptimizer:
    """特征优化器"""
    
    def __init__(self):
        self.learner = CustomWorkpieceLearner()
        self.original_features = None
        self.optimized_features = None
        self.feature_names = None
        self.feature_importance = None
        
    def analyze_current_features(self):
        """分析当前特征的有效性"""
        print("=" * 80)
        print("🔍 当前特征分析")
        print("=" * 80)
        
        # 准备训练数据
        X, y, labels = self.learner._prepare_training_data()
        if not X:
            print("❌ 没有训练数据")
            return None, None, None
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"训练样本数: {len(X)}")
        print(f"特征维度: {X.shape[1]}")
        print(f"工件类别数: {len(set(y))}")
        
        # 分析特征统计信息
        print(f"\n📊 特征统计:")
        print(f"  特征总数: {X.shape[1]}")
        print(f"  非零特征数: {np.count_nonzero(X.sum(axis=0))}")
        print(f"  零特征数: {np.sum(X.sum(axis=0) == 0)}")
        print(f"  非零特征占比: {np.count_nonzero(X.sum(axis=0)) / X.shape[1] * 100:.1f}%")
        
        # 分析特征分布
        feature_means = np.mean(X, axis=0)
        feature_stds = np.std(X, axis=0)
        
        print(f"\n📈 特征分布:")
        print(f"  特征均值范围: [{np.min(feature_means):.3f}, {np.max(feature_means):.3f}]")
        print(f"  特征标准差范围: [{np.min(feature_stds):.3f}, {np.max(feature_stds):.3f}]")
        print(f"  零方差特征数: {np.sum(feature_stds == 0)}")
        
        self.original_features = X
        return X, y, labels
    
    def create_feature_names(self):
        """创建特征名称"""
        feature_names = []
        
        # SIFT特征 (50 * 128 = 6400)
        for i in range(50):
            for j in range(128):
                feature_names.append(f"SIFT_{i}_{j}")
        
        # ORB特征 (50 * 32 = 1600)
        for i in range(50):
            for j in range(32):
                feature_names.append(f"ORB_{i}_{j}")
        
        # LBP特征 (256)
        for i in range(256):
            feature_names.append(f"LBP_{i}")
        
        # 颜色特征 (3 * 32 = 96)
        for channel in ['B', 'G', 'R']:
            for i in range(32):
                feature_names.append(f"Color_{channel}_{i}")
        
        # 形状特征 (5)
        shape_names = ['Area', 'Perimeter', 'AspectRatio', 'Extent', 'Circularity']
        feature_names.extend(shape_names)
        
        self.feature_names = feature_names
        return feature_names
    
    def analyze_feature_importance(self, X, y):
        """分析特征重要性"""
        print("\n" + "=" * 80)
        print("🎯 特征重要性分析")
        print("=" * 80)
        
        # 使用随机森林分析特征重要性
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        feature_importance = rf.feature_importances_
        self.feature_importance = feature_importance
        
        # 创建特征名称
        feature_names = self.create_feature_names()
        
        # 按重要性排序
        importance_indices = np.argsort(feature_importance)[::-1]
        
        print(f"📊 特征重要性统计:")
        print(f"  最高重要性: {feature_importance[importance_indices[0]]:.6f}")
        print(f"  最低重要性: {feature_importance[importance_indices[-1]]:.6f}")
        print(f"  重要性均值: {np.mean(feature_importance):.6f}")
        print(f"  重要性标准差: {np.std(feature_importance):.6f}")
        
        # 分析不同特征类型的重要性
        sift_importance = np.mean(feature_importance[:6400])
        orb_importance = np.mean(feature_importance[6400:8000])
        lbp_importance = np.mean(feature_importance[8000:8256])
        color_importance = np.mean(feature_importance[8256:8352])
        shape_importance = np.mean(feature_importance[8352:8357])
        
        print(f"\n🎨 各特征类型平均重要性:")
        print(f"  SIFT特征: {sift_importance:.6f}")
        print(f"  ORB特征: {orb_importance:.6f}")
        print(f"  LBP特征: {lbp_importance:.6f}")
        print(f"  颜色特征: {color_importance:.6f}")
        print(f"  形状特征: {shape_importance:.6f}")
        
        # 显示最重要的特征
        print(f"\n🏆 前20个最重要特征:")
        for i in range(min(20, len(importance_indices))):
            idx = importance_indices[i]
            print(f"  {i+1:2d}. {feature_names[idx]}: {feature_importance[idx]:.6f}")
        
        return feature_importance, importance_indices
    
    def select_features_by_importance(self, X, y, top_k=1000):
        """基于重要性选择特征"""
        print(f"\n" + "=" * 80)
        print(f"🎯 基于重要性选择前{top_k}个特征")
        print("=" * 80)
        
        # 分析特征重要性
        feature_importance, importance_indices = self.analyze_feature_importance(X, y)
        
        # 选择前k个重要特征
        selected_indices = importance_indices[:top_k]
        selected_features = X[:, selected_indices]
        
        print(f"✅ 特征选择完成:")
        print(f"  原始特征数: {X.shape[1]}")
        print(f"  选择特征数: {selected_features.shape[1]}")
        print(f"  特征减少: {X.shape[1] - selected_features.shape[1]} ({(X.shape[1] - selected_features.shape[1])/X.shape[1]*100:.1f}%)")
        
        return selected_features, selected_indices
    
    def select_features_by_variance(self, X, threshold=0.01):
        """基于方差选择特征"""
        print(f"\n" + "=" * 80)
        print(f"🎯 基于方差选择特征 (阈值: {threshold})")
        print("=" * 80)
        
        # 计算特征方差
        feature_vars = np.var(X, axis=0)
        
        # 选择方差大于阈值的特征
        selected_indices = np.where(feature_vars > threshold)[0]
        selected_features = X[:, selected_indices]
        
        print(f"✅ 方差特征选择完成:")
        print(f"  原始特征数: {X.shape[1]}")
        print(f"  选择特征数: {selected_features.shape[1]}")
        print(f"  特征减少: {X.shape[1] - selected_features.shape[1]} ({(X.shape[1] - selected_features.shape[1])/X.shape[1]*100:.1f}%)")
        print(f"  方差阈值: {threshold}")
        print(f"  最小方差: {np.min(feature_vars[selected_indices]):.6f}")
        print(f"  最大方差: {np.max(feature_vars[selected_indices]):.6f}")
        
        return selected_features, selected_indices
    
    def apply_pca(self, X, n_components=0.95):
        """应用PCA降维"""
        print(f"\n" + "=" * 80)
        print(f"🎯 PCA降维 (保留{n_components*100 if n_components < 1 else n_components}%方差)")
        print("=" * 80)
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 应用PCA
        pca = PCA(n_components=n_components, random_state=42)
        X_pca = pca.fit_transform(X_scaled)
        
        print(f"✅ PCA降维完成:")
        print(f"  原始特征数: {X.shape[1]}")
        print(f"  降维后特征数: {X_pca.shape[1]}")
        print(f"  特征减少: {X.shape[1] - X_pca.shape[1]} ({(X.shape[1] - X_pca.shape[1])/X.shape[1]*100:.1f}%)")
        print(f"  解释方差比: {pca.explained_variance_ratio_.sum():.3f}")
        
        return X_pca, pca, scaler
    
    def evaluate_feature_sets(self, X_original, X_selected, X_pca, y):
        """评估不同特征集的性能"""
        print(f"\n" + "=" * 80)
        print("🏆 特征集性能评估")
        print("=" * 80)
        
        feature_sets = {
            "原始特征": X_original,
            "重要性选择": X_selected,
            "PCA降维": X_pca
        }
        
        results = {}
        
        for name, X in feature_sets.items():
            if X is None:
                continue
                
            print(f"\n📊 评估 {name} ({X.shape[1]}维):")
            
            # 使用随机森林进行交叉验证
            rf = RandomForestClassifier(n_estimators=50, random_state=42)
            
            try:
                scores = cross_val_score(rf, X, y, cv=min(3, len(set(y))), scoring='accuracy')
                mean_score = np.mean(scores)
                std_score = np.std(scores)
                
                print(f"  交叉验证准确率: {mean_score:.3f} ± {std_score:.3f}")
                print(f"  特征维度: {X.shape[1]}")
                print(f"  计算复杂度: {X.shape[1] / X_original.shape[1]:.3f}x")
                
                results[name] = {
                    'accuracy': mean_score,
                    'std': std_score,
                    'dimensions': X.shape[1],
                    'complexity': X.shape[1] / X_original.shape[1]
                }
                
            except Exception as e:
                print(f"  ❌ 评估失败: {e}")
                results[name] = None
        
        # 推荐最佳特征集
        print(f"\n🎯 推荐:")
        best_name = None
        best_score = 0
        
        for name, result in results.items():
            if result and result['accuracy'] > best_score:
                best_score = result['accuracy']
                best_name = name
        
        if best_name:
            print(f"  推荐使用: {best_name}")
            print(f"  预期准确率: {best_score:.3f}")
            print(f"  特征维度: {results[best_name]['dimensions']}")
        
        return results
    
    def optimize_features(self):
        """执行特征优化"""
        print("🚀 开始特征工程优化...")
        
        # 1. 分析当前特征
        X, y, labels = self.analyze_current_features()
        if X is None:
            return False
        
        # 2. 基于重要性选择特征
        X_selected, selected_indices = self.select_features_by_importance(X, y, top_k=1000)
        
        # 3. 基于方差选择特征
        X_variance, variance_indices = self.select_features_by_variance(X, threshold=0.001)
        
        # 4. PCA降维
        X_pca, pca, scaler = self.apply_pca(X_variance, n_components=0.95)
        
        # 5. 评估不同特征集
        results = self.evaluate_feature_sets(X, X_selected, X_pca, y)
        
        # 6. 保存优化结果
        optimization_result = {
            'original_features': X.shape[1],
            'selected_features': X_selected.shape[1],
            'pca_features': X_pca.shape[1],
            'selected_indices': selected_indices.tolist(),
            'variance_indices': variance_indices.tolist(),
            'evaluation_results': results,
            'feature_importance': self.feature_importance.tolist() if self.feature_importance is not None else None
        }
        
        with open('feature_optimization_result.json', 'w', encoding='utf-8') as f:
            json.dump(optimization_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 特征优化完成，结果已保存到: feature_optimization_result.json")
        
        return True

def main():
    """主函数"""
    optimizer = FeatureOptimizer()
    success = optimizer.optimize_features()
    
    if success:
        print("\n🎉 特征工程优化成功完成！")
    else:
        print("\n❌ 特征工程优化失败")

if __name__ == "__main__":
    main()
