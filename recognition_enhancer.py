#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
识别准确率大幅提升增强器
集成多种高效的识别准确率提升方法
"""

import cv2
import numpy as np
import json
import os
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import joblib

class RecognitionEnhancer:
    """识别准确率增强器"""
    
    def __init__(self, base_learner=None):
        """
        初始化增强器
        
        Args:
            base_learner: 基础学习器 (CustomWorkpieceLearner 或 OptimizedWorkpieceLearner)
        """
        self.base_learner = base_learner
        self.logger = self._setup_logger()
        
        # 多模型集成
        self.ensemble_models = {}
        self.ensemble_scaler = StandardScaler()
        self.is_ensemble_trained = False
        
        # 图像预处理增强
        self.preprocessing_methods = [
            'histogram_equalization',
            'gaussian_blur',
            'bilateral_filter',
            'morphological_operations',
            'noise_reduction'
        ]
        
        # 多尺度检测
        self.scale_factors = [0.8, 1.0, 1.2]
        
        # 置信度融合权重
        self.confidence_weights = {
            'base_model': 0.4,
            'ensemble': 0.3,
            'multi_scale': 0.2,
            'preprocessing': 0.1
        }
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger('RecognitionEnhancer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def enhance_image_preprocessing(self, image: np.ndarray) -> List[np.ndarray]:
        """
        图像预处理增强
        生成多个预处理版本的图像
        """
        enhanced_images = []
        
        # 原始图像
        enhanced_images.append(image.copy())
        
        # 直方图均衡化
        if len(image.shape) == 3:
            # 彩色图像 - 在LAB空间进行
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            lab[:,:,0] = cv2.equalizeHist(lab[:,:,0])
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        else:
            enhanced = cv2.equalizeHist(image)
        enhanced_images.append(enhanced)
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(image, (3, 3), 0)
        enhanced_images.append(blurred)
        
        # 双边滤波
        bilateral = cv2.bilateralFilter(image, 9, 75, 75)
        enhanced_images.append(bilateral)
        
        # 形态学操作
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        kernel = np.ones((3,3), np.uint8)
        morphed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        if len(image.shape) == 3:
            morphed = cv2.cvtColor(morphed, cv2.COLOR_GRAY2BGR)
        enhanced_images.append(morphed)
        
        return enhanced_images
    
    def multi_scale_detection(self, image: np.ndarray) -> List[Dict]:
        """
        多尺度检测
        在不同尺度下进行检测，提高鲁棒性
        """
        all_results = []
        
        for scale in self.scale_factors:
            # 缩放图像
            height, width = image.shape[:2]
            new_height, new_width = int(height * scale), int(width * scale)
            scaled_image = cv2.resize(image, (new_width, new_height))
            
            # 使用基础学习器进行识别
            if self.base_learner:
                result = self.base_learner.recognize_workpiece(scaled_image)
                if result['success']:
                    # 调整坐标回原始尺度
                    result['center_x'] = int(result['center_x'] / scale)
                    result['center_y'] = int(result['center_y'] / scale)
                    result['scale_factor'] = scale
                    all_results.append(result)
        
        return all_results
    
    def train_ensemble_models(self, X, y):
        """
        训练集成模型
        使用多种不同的算法进行集成学习
        """
        try:
            self.logger.info("开始训练集成模型...")
            
            # 数据标准化
            X_scaled = self.ensemble_scaler.fit_transform(X)
            
            # 定义多个基础模型
            models = {
                'rf': RandomForestClassifier(
                    n_estimators=200,
                    max_depth=15,
                    min_samples_split=3,
                    random_state=42
                ),
                'svm': SVC(
                    kernel='rbf',
                    C=1.0,
                    gamma='scale',
                    probability=True,
                    random_state=42
                ),
                'mlp': MLPClassifier(
                    hidden_layer_sizes=(100, 50),
                    max_iter=500,
                    random_state=42
                )
            }
            
            # 训练各个模型
            for name, model in models.items():
                self.logger.info(f"训练 {name} 模型...")
                model.fit(X_scaled, y)
                
                # 交叉验证评估
                scores = cross_val_score(model, X_scaled, y, cv=3)
                self.logger.info(f"{name} 交叉验证准确率: {scores.mean():.3f} ± {scores.std():.3f}")
                
                self.ensemble_models[name] = model
            
            # 创建投票分类器
            voting_models = [(name, model) for name, model in self.ensemble_models.items()]
            self.ensemble_classifier = VotingClassifier(
                estimators=voting_models,
                voting='soft'  # 使用概率投票
            )
            
            self.ensemble_classifier.fit(X_scaled, y)
            
            # 评估集成模型
            ensemble_scores = cross_val_score(self.ensemble_classifier, X_scaled, y, cv=3)
            self.logger.info(f"集成模型交叉验证准确率: {ensemble_scores.mean():.3f} ± {ensemble_scores.std():.3f}")
            
            self.is_ensemble_trained = True
            self.logger.info("集成模型训练完成")
            
            return True
            
        except Exception as e:
            self.logger.error(f"集成模型训练失败: {e}")
            return False
    
    def ensemble_predict(self, features):
        """
        使用集成模型进行预测
        """
        if not self.is_ensemble_trained:
            return None
        
        try:
            features_scaled = self.ensemble_scaler.transform([features])
            
            # 获取各个模型的预测
            predictions = {}
            confidences = {}
            
            for name, model in self.ensemble_models.items():
                pred = model.predict(features_scaled)[0]
                prob = model.predict_proba(features_scaled)[0]
                predictions[name] = pred
                confidences[name] = np.max(prob)
            
            # 集成模型预测
            ensemble_pred = self.ensemble_classifier.predict(features_scaled)[0]
            ensemble_prob = self.ensemble_classifier.predict_proba(features_scaled)[0]
            ensemble_confidence = np.max(ensemble_prob)
            
            return {
                'individual_predictions': predictions,
                'individual_confidences': confidences,
                'ensemble_prediction': ensemble_pred,
                'ensemble_confidence': ensemble_confidence
            }
            
        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            return None
    
    def enhanced_recognition(self, image: np.ndarray) -> Dict:
        """
        增强识别主函数
        集成多种方法提升识别准确率
        """
        try:
            self.logger.info("开始增强识别...")
            
            all_results = []
            
            # 1. 基础识别
            if self.base_learner:
                base_result = self.base_learner.recognize_workpiece(image)
                if base_result['success']:
                    base_result['method'] = 'base'
                    all_results.append(base_result)
            
            # 2. 预处理增强识别
            enhanced_images = self.enhance_image_preprocessing(image)
            for i, enhanced_img in enumerate(enhanced_images[1:], 1):  # 跳过原始图像
                if self.base_learner:
                    result = self.base_learner.recognize_workpiece(enhanced_img)
                    if result['success']:
                        result['method'] = f'preprocessing_{i}'
                        all_results.append(result)
            
            # 3. 多尺度检测
            multi_scale_results = self.multi_scale_detection(image)
            for result in multi_scale_results:
                result['method'] = f'multi_scale_{result["scale_factor"]}'
                all_results.append(result)
            
            # 4. 结果融合
            if all_results:
                final_result = self.fuse_results(all_results)
                self.logger.info(f"增强识别完成，融合了 {len(all_results)} 个结果")
                return final_result
            else:
                return {'success': False, 'error': '所有方法都未能识别出工件'}
                
        except Exception as e:
            self.logger.error(f"增强识别失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def fuse_results(self, results: List[Dict]) -> Dict:
        """
        融合多个识别结果
        使用加权投票和置信度融合
        """
        if not results:
            return {'success': False, 'error': '没有结果可融合'}
        
        # 按工件名称分组
        workpiece_groups = {}
        for result in results:
            name = result['workpiece_name']
            if name not in workpiece_groups:
                workpiece_groups[name] = []
            workpiece_groups[name].append(result)
        
        # 计算每个工件的加权置信度
        weighted_scores = {}
        for name, group in workpiece_groups.items():
            total_weight = 0
            weighted_confidence = 0
            
            for result in group:
                method = result.get('method', 'base')
                
                # 根据方法分配权重
                if 'base' in method:
                    weight = self.confidence_weights['base_model']
                elif 'preprocessing' in method:
                    weight = self.confidence_weights['preprocessing']
                elif 'multi_scale' in method:
                    weight = self.confidence_weights['multi_scale']
                else:
                    weight = 0.1
                
                weighted_confidence += result['confidence'] * weight
                total_weight += weight
            
            if total_weight > 0:
                weighted_scores[name] = {
                    'confidence': weighted_confidence / total_weight,
                    'count': len(group),
                    'results': group
                }
        
        # 选择最佳结果
        if weighted_scores:
            best_name = max(weighted_scores.keys(), 
                          key=lambda x: weighted_scores[x]['confidence'] * weighted_scores[x]['count'])
            
            best_group = weighted_scores[best_name]
            best_result = best_group['results'][0].copy()  # 使用第一个结果作为基础
            
            # 更新置信度
            best_result['confidence'] = best_group['confidence']
            best_result['fusion_count'] = best_group['count']
            best_result['enhanced'] = True
            
            self.logger.info(f"融合结果: {best_name}, 置信度: {best_result['confidence']:.3f}, 融合数量: {best_group['count']}")
            
            return best_result
        
        return {'success': False, 'error': '融合失败'}
    
    def save_enhanced_model(self, filepath: str):
        """保存增强模型"""
        if self.is_ensemble_trained:
            model_data = {
                'ensemble_models': self.ensemble_models,
                'ensemble_classifier': self.ensemble_classifier,
                'ensemble_scaler': self.ensemble_scaler,
                'confidence_weights': self.confidence_weights
            }
            joblib.dump(model_data, filepath)
            self.logger.info(f"增强模型已保存到: {filepath}")
    
    def load_enhanced_model(self, filepath: str):
        """加载增强模型"""
        if os.path.exists(filepath):
            model_data = joblib.load(filepath)
            self.ensemble_models = model_data['ensemble_models']
            self.ensemble_classifier = model_data['ensemble_classifier']
            self.ensemble_scaler = model_data['ensemble_scaler']
            self.confidence_weights = model_data['confidence_weights']
            self.is_ensemble_trained = True
            self.logger.info(f"增强模型已从 {filepath} 加载")
            return True
        return False
