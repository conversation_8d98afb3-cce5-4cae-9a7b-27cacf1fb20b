#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强识别效果
对比原始识别和增强识别的性能差异
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime

sys.path.append('.')
from custom_workpiece_learner import CustomWorkpieceLearner

def test_enhanced_vs_original():
    """对比增强识别和原始识别"""
    print("🎯 增强识别 vs 原始识别对比测试")
    print("=" * 60)
    
    # 初始化学习器
    learner = CustomWorkpieceLearner()
    
    # 获取测试图像
    images_dir = "./workpiece_data/images"
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    
    if not image_files:
        print("❌ 未找到测试图像")
        return
    
    print(f"找到 {len(image_files)} 个测试图像")
    
    # 测试结果
    original_results = []
    enhanced_results = []
    
    # 测试前10个图像
    test_count = min(10, len(image_files))
    
    for i, image_path in enumerate(image_files[:test_count]):
        image_name = os.path.basename(image_path)
        print(f"\n📷 测试 {i+1}/{test_count}: {image_name}")
        
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            # 原始识别
            original_result = learner.recognize_workpiece(image)
            original_results.append((image_name, original_result))
            
            if original_result['success']:
                print(f"  原始: ✅ {original_result['workpiece_name']} ({original_result['confidence']:.3f})")
            else:
                print(f"  原始: ❌ {original_result.get('error', '失败')}")
            
            # 增强识别
            enhanced_result = learner.enhanced_recognize_workpiece(image)
            enhanced_results.append((image_name, enhanced_result))
            
            if enhanced_result['success']:
                fusion_count = enhanced_result.get('fusion_count', 1)
                best_method = enhanced_result.get('best_method', 'unknown')
                print(f"  增强: ✅ {enhanced_result['workpiece_name']} ({enhanced_result['confidence']:.3f}) [融合{fusion_count}个, 最佳:{best_method}]")
            else:
                print(f"  增强: ❌ {enhanced_result.get('error', '失败')}")
                
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    return original_results, enhanced_results

def analyze_performance_improvement(original_results, enhanced_results):
    """分析性能改进"""
    print("\n📊 性能改进分析")
    print("=" * 60)
    
    # 统计成功率
    original_success = len([r for _, r in original_results if r['success']])
    enhanced_success = len([r for _, r in enhanced_results if r['success']])
    total_tests = len(original_results)
    
    print(f"📈 识别成功率对比:")
    print(f"  原始识别: {original_success}/{total_tests} ({original_success/total_tests*100:.1f}%)")
    print(f"  增强识别: {enhanced_success}/{total_tests} ({enhanced_success/total_tests*100:.1f}%)")
    
    success_improvement = enhanced_success - original_success
    print(f"  成功率提升: {success_improvement:+d} ({success_improvement/total_tests*100:+.1f}%)")
    
    # 置信度分析
    original_confidences = [r['confidence'] for _, r in original_results if r['success']]
    enhanced_confidences = [r['confidence'] for _, r in enhanced_results if r['success']]
    
    if original_confidences and enhanced_confidences:
        print(f"\n📊 置信度对比:")
        print(f"  原始识别平均置信度: {np.mean(original_confidences):.3f}")
        print(f"  增强识别平均置信度: {np.mean(enhanced_confidences):.3f}")
        
        confidence_improvement = np.mean(enhanced_confidences) - np.mean(original_confidences)
        improvement_percent = (confidence_improvement / np.mean(original_confidences)) * 100
        print(f"  置信度提升: {confidence_improvement:+.3f} ({improvement_percent:+.1f}%)")
        
        # 高置信度结果统计
        original_high = len([c for c in original_confidences if c >= 0.7])
        enhanced_high = len([c for c in enhanced_confidences if c >= 0.7])
        
        print(f"\n🎯 高置信度结果 (≥0.7):")
        print(f"  原始识别: {original_high} 个 ({original_high/len(original_confidences)*100:.1f}%)")
        print(f"  增强识别: {enhanced_high} 个 ({enhanced_high/len(enhanced_confidences)*100:.1f}%)")
        print(f"  高置信度提升: {enhanced_high - original_high:+d} 个")
    
    # 融合效果分析
    fusion_stats = []
    method_stats = {}
    
    for _, result in enhanced_results:
        if result['success']:
            fusion_count = result.get('fusion_count', 1)
            best_method = result.get('best_method', 'unknown')
            
            fusion_stats.append(fusion_count)
            
            if best_method not in method_stats:
                method_stats[best_method] = 0
            method_stats[best_method] += 1
    
    if fusion_stats:
        print(f"\n🔗 结果融合统计:")
        print(f"  平均融合结果数: {np.mean(fusion_stats):.1f}")
        print(f"  最大融合结果数: {max(fusion_stats)}")
        
        multi_fusion = len([c for c in fusion_stats if c > 1])
        print(f"  多结果融合: {multi_fusion} 个 ({multi_fusion/len(fusion_stats)*100:.1f}%)")
        
        print(f"\n🏆 最佳方法统计:")
        for method, count in sorted(method_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {method}: {count} 次 ({count/len(fusion_stats)*100:.1f}%)")

def generate_improvement_report(original_results, enhanced_results):
    """生成改进报告"""
    print("\n📋 识别准确率大幅提升报告")
    print("=" * 60)
    
    # 计算关键指标
    original_success_rate = len([r for _, r in original_results if r['success']]) / len(original_results) * 100
    enhanced_success_rate = len([r for _, r in enhanced_results if r['success']]) / len(enhanced_results) * 100
    
    original_confidences = [r['confidence'] for _, r in original_results if r['success']]
    enhanced_confidences = [r['confidence'] for _, r in enhanced_results if r['success']]
    
    original_avg_conf = np.mean(original_confidences) if original_confidences else 0
    enhanced_avg_conf = np.mean(enhanced_confidences) if enhanced_confidences else 0
    
    print(f"🎯 核心改进指标:")
    print(f"  识别成功率: {original_success_rate:.1f}% → {enhanced_success_rate:.1f}% ({enhanced_success_rate - original_success_rate:+.1f}%)")
    print(f"  平均置信度: {original_avg_conf:.3f} → {enhanced_avg_conf:.3f} ({enhanced_avg_conf - original_avg_conf:+.3f})")
    
    if enhanced_avg_conf > 0:
        relative_improvement = ((enhanced_avg_conf - original_avg_conf) / original_avg_conf) * 100
        print(f"  相对置信度提升: {relative_improvement:+.1f}%")
    
    print(f"\n🚀 增强技术效果:")
    print(f"  ✅ 图像预处理增强: 直方图均衡化、高斯模糊、双边滤波、形态学操作、对比度增强")
    print(f"  ✅ 多尺度检测: 0.8x, 1.2x, 1.5x 尺度检测")
    print(f"  ✅ 智能结果融合: 加权投票和多方法确认")
    print(f"  ✅ 自动最佳方法选择: 动态选择最优预处理方法")
    
    print(f"\n💡 使用建议:")
    if enhanced_success_rate > original_success_rate:
        print(f"  🎉 增强识别显著提升了性能，建议在生产环境中使用")
        print(f"  🔧 在 vision_demo.py 中已自动启用增强识别")
        print(f"  📊 可以通过界面查看融合结果数和最佳方法信息")
    else:
        print(f"  ⚠️ 增强识别未显著提升性能，可能需要:")
        print(f"     - 检查训练样本质量")
        print(f"     - 调整增强参数")
        print(f"     - 增加更多训练数据")
    
    # 保存报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_count': len(original_results),
        'original_performance': {
            'success_rate': original_success_rate,
            'avg_confidence': original_avg_conf,
            'confidences': original_confidences
        },
        'enhanced_performance': {
            'success_rate': enhanced_success_rate,
            'avg_confidence': enhanced_avg_conf,
            'confidences': enhanced_confidences
        },
        'improvements': {
            'success_rate_improvement': enhanced_success_rate - original_success_rate,
            'confidence_improvement': enhanced_avg_conf - original_avg_conf,
            'relative_improvement': relative_improvement if enhanced_avg_conf > 0 else 0
        }
    }
    
    with open('enhanced_recognition_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: enhanced_recognition_report.json")

def main():
    """主函数"""
    print("🎯 测试增强识别效果")
    print("验证多种增强技术对识别准确率的提升效果...")
    
    try:
        # 1. 对比测试
        original_results, enhanced_results = test_enhanced_vs_original()
        
        # 2. 分析性能改进
        if original_results and enhanced_results:
            analyze_performance_improvement(original_results, enhanced_results)
            
            # 3. 生成改进报告
            generate_improvement_report(original_results, enhanced_results)
        
        print("\n" + "=" * 60)
        print("✅ 增强识别测试完成")
        print("=" * 60)
        
        print("\n🎯 总结:")
        print("增强识别方法已集成到您的系统中，包括:")
        print("1. 📸 5种图像预处理技术")
        print("2. 🔍 3种尺度的多尺度检测")
        print("3. 🎯 智能结果融合算法")
        print("4. 🏆 自动最佳方法选择")
        print("\n现在运行 vision_demo.py 即可体验增强识别效果！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
