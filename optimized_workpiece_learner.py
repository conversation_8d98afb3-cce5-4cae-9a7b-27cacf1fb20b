#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的工件学习识别系统
集成特征选择、动态阈值调整和模型优化
"""

import cv2
import numpy as np
import json
import os
import pickle
import time
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import accuracy_score, classification_report
import joblib

class OptimizedWorkpieceLearner:
    """优化后的工件学习识别器"""
    
    def __init__(self, data_dir: str = "./workpiece_data"):
        """初始化优化后的学习器"""
        self.data_dir = data_dir
        self.models_dir = os.path.join(data_dir, "models")
        self.images_dir = os.path.join(data_dir, "images")
        self.annotations_dir = os.path.join(data_dir, "annotations")
        
        # 创建必要的目录
        for dir_path in [self.data_dir, self.models_dir, self.images_dir, self.annotations_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 设置日志器
        self.logger = self._setup_logger()
        
        # 工件数据库
        self.workpiece_database = {}
        self.load_workpiece_database()
        
        # 机器学习模型
        self.classifier = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # 特征优化配置
        self.use_feature_selection = True
        self.selected_feature_indices = None
        self.load_feature_optimization()
        
        # 动态阈值配置
        self.dynamic_thresholds = {}
        self.default_threshold = 0.45  # 降低默认阈值
        self.load_threshold_config()
        
        # 优化后的模型参数
        self.optimized_params = {
            'n_estimators': 200,
            'max_depth': 15,
            'min_samples_split': 3,
            'min_samples_leaf': 2,
            'random_state': 42
        }
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger('OptimizedWorkpieceLearner')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_workpiece_database(self):
        """加载工件数据库"""
        db_path = os.path.join(self.data_dir, "workpiece_database.json")
        if os.path.exists(db_path):
            try:
                with open(db_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        # 转换列表格式为字典格式
                        self.workpiece_database = {}
                        for item in data:
                            if 'id' in item:
                                self.workpiece_database[item['id']] = item
                    else:
                        self.workpiece_database = data
                
                self.logger.info(f"工件数据库已加载: {len(self.workpiece_database)} 个工件")
            except Exception as e:
                self.logger.error(f"加载工件数据库失败: {e}")
                self.workpiece_database = {}
        else:
            self.workpiece_database = {}
    
    def load_feature_optimization(self):
        """加载特征优化配置"""
        opt_path = "feature_optimization_result.json"
        if os.path.exists(opt_path):
            try:
                with open(opt_path, 'r', encoding='utf-8') as f:
                    opt_data = json.load(f)
                    self.selected_feature_indices = np.array(opt_data['selected_indices'])
                    self.logger.info(f"特征优化配置已加载: {len(self.selected_feature_indices)} 个选择特征")
            except Exception as e:
                self.logger.error(f"加载特征优化配置失败: {e}")
                self.selected_feature_indices = None
    
    def load_threshold_config(self):
        """加载阈值配置"""
        threshold_path = os.path.join(self.models_dir, "threshold_config.json")
        if os.path.exists(threshold_path):
            try:
                with open(threshold_path, 'r', encoding='utf-8') as f:
                    self.dynamic_thresholds = json.load(f)
                    self.logger.info("动态阈值配置已加载")
            except Exception as e:
                self.logger.error(f"加载阈值配置失败: {e}")
    
    def save_threshold_config(self):
        """保存阈值配置"""
        threshold_path = os.path.join(self.models_dir, "threshold_config.json")
        try:
            with open(threshold_path, 'w', encoding='utf-8') as f:
                json.dump(self.dynamic_thresholds, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存阈值配置失败: {e}")
    
    def extract_features(self, image: np.ndarray) -> np.ndarray:
        """提取优化后的特征"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            features = []
            
            # SIFT特征
            sift_features = self._extract_sift_features(gray)
            features.extend(sift_features)
            
            # ORB特征
            orb_features = self._extract_orb_features(gray)
            features.extend(orb_features)
            
            # LBP纹理特征
            lbp_features = self._extract_lbp_features(gray)
            features.extend(lbp_features)
            
            # 颜色直方图特征
            color_features = self._extract_color_features(image)
            features.extend(color_features)
            
            # 形状特征
            shape_features = self._extract_shape_features(gray)
            features.extend(shape_features)
            
            all_features = np.array(features, dtype=np.float32)
            
            # 应用特征选择
            if self.use_feature_selection and self.selected_feature_indices is not None:
                if len(all_features) >= len(self.selected_feature_indices):
                    selected_features = all_features[self.selected_feature_indices]
                    return selected_features
            
            return all_features
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return np.array([])
    
    def _extract_sift_features(self, gray: np.ndarray, max_features: int = 50) -> List[float]:
        """提取SIFT特征"""
        try:
            sift = cv2.SIFT_create()
            keypoints, descriptors = sift.detectAndCompute(gray, None)
            
            if descriptors is not None:
                descriptors = descriptors[:max_features]
                features = descriptors.flatten()
                target_length = max_features * 128
                if len(features) < target_length:
                    features = np.pad(features, (0, target_length - len(features)))
                else:
                    features = features[:target_length]
                return features.tolist()
            else:
                return [0.0] * (max_features * 128)
        except:
            return [0.0] * (max_features * 128)
    
    def _extract_orb_features(self, gray: np.ndarray, max_features: int = 50) -> List[float]:
        """提取ORB特征"""
        try:
            orb = cv2.ORB_create(nfeatures=max_features)
            keypoints, descriptors = orb.detectAndCompute(gray, None)
            
            if descriptors is not None:
                features = descriptors.flatten().astype(np.float32)
                target_length = max_features * 32
                if len(features) < target_length:
                    features = np.pad(features, (0, target_length - len(features)))
                else:
                    features = features[:target_length]
                return features.tolist()
            else:
                return [0.0] * (max_features * 32)
        except:
            return [0.0] * (max_features * 32)
    
    def _extract_lbp_features(self, gray: np.ndarray) -> List[float]:
        """提取LBP纹理特征"""
        try:
            lbp = np.zeros_like(gray)
            for i in range(1, gray.shape[0]-1):
                for j in range(1, gray.shape[1]-1):
                    center = gray[i, j]
                    code = 0
                    code |= (gray[i-1, j-1] >= center) << 7
                    code |= (gray[i-1, j] >= center) << 6
                    code |= (gray[i-1, j+1] >= center) << 5
                    code |= (gray[i, j+1] >= center) << 4
                    code |= (gray[i+1, j+1] >= center) << 3
                    code |= (gray[i+1, j] >= center) << 2
                    code |= (gray[i+1, j-1] >= center) << 1
                    code |= (gray[i, j-1] >= center) << 0
                    lbp[i, j] = code
            
            hist, _ = np.histogram(lbp.ravel(), bins=256, range=(0, 256))
            hist = hist.astype(np.float32)
            hist /= (hist.sum() + 1e-7)
            return hist.tolist()
        except:
            return [0.0] * 256
    
    def _extract_color_features(self, image: np.ndarray) -> List[float]:
        """提取颜色特征"""
        try:
            if len(image.shape) == 3:
                features = []
                for i in range(3):
                    hist = cv2.calcHist([image], [i], None, [32], [0, 256])
                    hist = hist.flatten()
                    hist = hist / (hist.sum() + 1e-7)
                    features.extend(hist.tolist())
                return features
            else:
                hist = cv2.calcHist([image], [0], None, [32], [0, 256])
                hist = hist.flatten()
                hist = hist / (hist.sum() + 1e-7)
                return hist.tolist()
        except:
            return [0.0] * 96
    
    def _extract_shape_features(self, gray: np.ndarray) -> List[float]:
        """提取形状特征"""
        try:
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(largest_contour)
                perimeter = cv2.arcLength(largest_contour, True)
                x, y, w, h = cv2.boundingRect(largest_contour)
                aspect_ratio = w / h if h > 0 else 0
                rect_area = w * h
                extent = area / rect_area if rect_area > 0 else 0
                circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
                return [area, perimeter, aspect_ratio, extent, circularity]
            else:
                return [0.0, 0.0, 1.0, 0.0, 0.0]
        except:
            return [0.0, 0.0, 1.0, 0.0, 0.0]

    def _prepare_training_data(self) -> Tuple[List, List, List]:
        """准备训练数据"""
        X, y, labels = [], [], []
        label_map = {}
        current_label = 0

        for workpiece_id, record in self.workpiece_database.items():
            workpiece_name = record['info'].get('name', workpiece_id)

            if workpiece_name not in label_map:
                label_map[workpiece_name] = current_label
                labels.append(workpiece_name)
                current_label += 1

            label = label_map[workpiece_name]

            # 添加主样本
            if 'features' in record and record['features']:
                X.append(record['features'])
                y.append(label)

            # 添加额外样本
            if 'additional_samples' in record:
                for sample in record['additional_samples']:
                    if 'features' in sample and sample['features']:
                        X.append(sample['features'])
                        y.append(label)

        return X, y, labels

    def train_optimized_model(self) -> bool:
        """训练优化后的模型"""
        try:
            if len(self.workpiece_database) < 2:
                self.logger.warning("需要至少2种工件才能训练模型")
                return False

            # 准备训练数据
            X, y, labels = self._prepare_training_data()

            if len(X) == 0:
                self.logger.error("没有有效的训练数据")
                return False

            X = np.array(X)
            y = np.array(y)

            # 应用特征选择
            if self.use_feature_selection and self.selected_feature_indices is not None:
                if X.shape[1] >= len(self.selected_feature_indices):
                    X = X[:, self.selected_feature_indices]
                    self.logger.info(f"应用特征选择: {X.shape[1]} 个特征")

            # 数据标准化
            X_scaled = self.scaler.fit_transform(X)

            # 分割训练和测试数据
            unique_classes = len(set(y))
            min_samples_per_class = min([list(y).count(cls) for cls in set(y)])

            if len(X) > unique_classes * 2 and min_samples_per_class >= 2:
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y, test_size=0.2, random_state=42, stratify=y
                )
            else:
                X_train, X_test, y_train, y_test = X_scaled, X_scaled, y, y

            # 使用优化后的参数训练分类器
            self.classifier = RandomForestClassifier(**self.optimized_params)
            self.classifier.fit(X_train, y_train)

            # 评估模型
            y_pred = self.classifier.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)

            self.logger.info(f"优化模型训练完成，准确率: {accuracy:.3f}")

            # 计算每个类别的动态阈值
            self._calculate_dynamic_thresholds(X_scaled, y, labels)

            # 保存模型
            model_path = os.path.join(self.models_dir, "optimized_workpiece_classifier.pkl")
            scaler_path = os.path.join(self.models_dir, "optimized_feature_scaler.pkl")

            joblib.dump(self.classifier, model_path)
            joblib.dump(self.scaler, scaler_path)

            # 保存训练信息
            model_info = {
                'is_trained': True,
                'trained_time': datetime.now().isoformat(),
                'accuracy': accuracy,
                'workpiece_count': len(labels),
                'feature_count': X.shape[1],
                'sample_count': len(X),
                'labels': labels,
                'optimized_params': self.optimized_params,
                'use_feature_selection': self.use_feature_selection
            }

            info_path = os.path.join(self.models_dir, "optimized_model_info.json")
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(model_info, f, ensure_ascii=False, indent=2)

            self.is_trained = True
            self.logger.info("优化模型已保存")
            return True

        except Exception as e:
            self.logger.error(f"优化模型训练失败: {e}")
            return False

    def _calculate_dynamic_thresholds(self, X, y, labels):
        """计算动态阈值"""
        try:
            # 为每个类别计算置信度分布
            probabilities = self.classifier.predict_proba(X)

            for i, label in enumerate(labels):
                class_indices = np.where(y == i)[0]
                if len(class_indices) > 0:
                    class_probs = probabilities[class_indices, i]
                    # 使用更低的分位数作为阈值，确保更高的召回率
                    threshold = np.percentile(class_probs, 10)  # 10%分位数，更宽松
                    # 设置更合理的阈值范围
                    self.dynamic_thresholds[label] = max(min(threshold, 0.5), 0.35)  # 阈值范围0.35-0.5

            self.save_threshold_config()
            self.logger.info(f"动态阈值已计算: {self.dynamic_thresholds}")

        except Exception as e:
            self.logger.error(f"计算动态阈值失败: {e}")

    def load_optimized_model(self) -> bool:
        """加载优化后的模型"""
        try:
            model_path = os.path.join(self.models_dir, "optimized_workpiece_classifier.pkl")
            scaler_path = os.path.join(self.models_dir, "optimized_feature_scaler.pkl")
            info_path = os.path.join(self.models_dir, "optimized_model_info.json")

            if all(os.path.exists(p) for p in [model_path, scaler_path, info_path]):
                self.classifier = joblib.load(model_path)
                self.scaler = joblib.load(scaler_path)

                with open(info_path, 'r', encoding='utf-8') as f:
                    model_info = json.load(f)
                    self.is_trained = model_info.get('is_trained', False)

                self.logger.info("优化模型加载成功")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"加载优化模型失败: {e}")
            return False

    def recognize_workpiece_optimized(self, image: np.ndarray, bbox: Optional[Tuple[int, int, int, int]] = None) -> Dict:
        """优化后的工件识别"""
        try:
            if not self.is_trained:
                if not self.load_optimized_model():
                    return {'success': False, 'error': '优化模型未训练或加载失败'}

            # 如果没有提供边界框，尝试自动检测
            if bbox is None:
                detected_objects = self._detect_objects(image)
                if not detected_objects:
                    return {'success': False, 'error': '未检测到工件'}
                bbox = detected_objects[0]

            # 提取工件区域
            x, y, w, h = bbox
            workpiece_roi = image[y:y+h, x:x+w]

            # 提取优化后的特征
            features = self.extract_features(workpiece_roi)

            if len(features) == 0:
                return {'success': False, 'error': '特征提取失败'}

            # 预测
            features_scaled = self.scaler.transform([features])
            prediction = self.classifier.predict(features_scaled)[0]
            probabilities = self.classifier.predict_proba(features_scaled)[0]
            confidence = np.max(probabilities)

            # 获取预测的工件名称
            _, _, labels = self._prepare_training_data()
            predicted_workpiece = labels[prediction] if prediction < len(labels) else "Unknown"

            # 使用动态阈值
            threshold = self.dynamic_thresholds.get(predicted_workpiece, self.default_threshold)

            # 如果置信度低于动态阈值，返回失败
            if confidence < threshold:
                return {
                    'success': False,
                    'error': f'置信度过低 ({confidence:.3f} < {threshold:.3f})',
                    'confidence': float(confidence),
                    'threshold': threshold
                }

            # 查找对应的工件信息
            workpiece_info = None
            for record in self.workpiece_database.values():
                if record['info'].get('name') == predicted_workpiece:
                    workpiece_info = record['info'].copy()
                    break

            result = {
                'success': True,
                'workpiece_name': predicted_workpiece,
                'confidence': float(confidence),
                'threshold': threshold,
                'bbox': bbox,
                'center_x': x + w // 2,
                'center_y': y + h // 2,
                'workpiece_info': workpiece_info,
                'timestamp': datetime.now().isoformat(),
                'optimized': True
            }

            self.logger.info(f"优化识别结果: {predicted_workpiece} (置信度: {confidence:.3f}, 阈值: {threshold:.3f})")
            return result

        except Exception as e:
            self.logger.error(f"优化工件识别失败: {e}")
            return {'success': False, 'error': str(e)}

    def _detect_objects(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """自动检测图像中的对象"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 使用自适应阈值和轮廓检测
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY_INV, 11, 2)

            # 形态学操作
            kernel = np.ones((3, 3), np.uint8)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 过滤和排序轮廓
            valid_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 500:  # 最小面积阈值
                    valid_contours.append(contour)

            # 按面积排序
            valid_contours.sort(key=cv2.contourArea, reverse=True)

            # 转换为边界框
            bboxes = []
            for contour in valid_contours[:5]:  # 最多返回5个对象
                x, y, w, h = cv2.boundingRect(contour)
                bboxes.append((x, y, w, h))

            return bboxes

        except Exception as e:
            self.logger.error(f"对象检测失败: {e}")
            return []

    def get_optimization_info(self) -> Dict:
        """获取优化信息"""
        info = {
            'feature_selection_enabled': self.use_feature_selection,
            'selected_features_count': len(self.selected_feature_indices) if self.selected_feature_indices is not None else 0,
            'dynamic_thresholds': self.dynamic_thresholds.copy(),
            'default_threshold': self.default_threshold,
            'optimized_params': self.optimized_params.copy(),
            'is_trained': self.is_trained
        }
        return info
