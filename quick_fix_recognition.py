#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复识别问题
"""

import sys
sys.path.append('.')

def quick_fix():
    """快速修复"""
    try:
        from custom_workpiece_learner import CustomWorkpieceLearner
        
        print("🔧 执行快速修复...")
        
        # 1. 降低置信度阈值
        learner = CustomWorkpieceLearner()
        
        # 2. 重新训练模型
        print("重新训练模型...")
        success = learner.train_model()
        
        if success:
            print("✅ 快速修复完成")
            print("建议:")
            print("1. 重启 vision_demo.py")
            print("2. 测试绿色小铁片识别")
            print("3. 如果仍有问题，请添加更多样本")
        else:
            print("❌ 修复失败，请检查训练数据")
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")

if __name__ == "__main__":
    quick_fix()
