#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析脚本 - 分析识别准确率和置信度问题
"""

import sys
import os
sys.path.append('.')

from custom_workpiece_learner import CustomWorkpieceLearner
from vision_workpiece_detector import VisionWorkpieceDetector
import cv2
import numpy as np
import json

def analyze_model_status():
    """分析模型状态"""
    print("=" * 60)
    print("🔍 模型状态分析")
    print("=" * 60)
    
    learner = CustomWorkpieceLearner()
    
    # 检查模型信息
    model_info = learner.get_model_info()
    print(f"模型已训练: {model_info['is_trained']}")
    print(f"模型文件存在: {model_info['model_exists']}")
    print(f"工件数量: {model_info['workpiece_count']}")
    print(f"特征类型: {model_info['feature_type']}")
    
    if 'trained_time' in model_info:
        print(f"训练时间: {model_info['trained_time']}")
    if 'trained_workpiece_count' in model_info:
        print(f"训练时工件数量: {model_info['trained_workpiece_count']}")
    
    return learner, model_info

def analyze_workpiece_database(learner):
    """分析工件数据库"""
    print("\n" + "=" * 60)
    print("📊 工件数据库分析")
    print("=" * 60)
    
    workpiece_list = learner.get_workpiece_list()
    print(f"总工件数量: {len(workpiece_list)}")
    
    for i, wp in enumerate(workpiece_list, 1):
        print(f"\n工件 {i}:")
        print(f"  ID: {wp['id']}")
        print(f"  名称: {wp['name']}")
        print(f"  类型: {wp['type']}")
        print(f"  学习次数: {wp['learn_count']}")
        print(f"  创建时间: {wp['created_time']}")
        
        # 显示详细信息
        info = wp['info']
        if info.get('specifications'):
            print(f"  规格: {info['specifications']}")
        if info.get('material'):
            print(f"  材料: {info['material']}")
        if info.get('supplier'):
            print(f"  供应商: {info['supplier']}")
        if info.get('notes'):
            print(f"  备注: {info['notes']}")
    
    return workpiece_list

def test_feature_extraction(learner):
    """测试特征提取"""
    print("\n" + "=" * 60)
    print("🧪 特征提取测试")
    print("=" * 60)
    
    # 创建测试图像
    test_images = []
    
    # 测试图像1: 简单矩形
    img1 = np.zeros((200, 200, 3), dtype=np.uint8)
    img1.fill(50)  # 深灰背景
    cv2.rectangle(img1, (50, 50), (150, 150), (200, 100, 100), -1)
    test_images.append(("矩形", img1))
    
    # 测试图像2: 圆形
    img2 = np.zeros((200, 200, 3), dtype=np.uint8)
    img2.fill(50)
    cv2.circle(img2, (100, 100), 60, (100, 200, 100), -1)
    test_images.append(("圆形", img2))
    
    # 测试图像3: 复杂形状
    img3 = np.zeros((200, 200, 3), dtype=np.uint8)
    img3.fill(50)
    cv2.rectangle(img3, (30, 30), (170, 170), (150, 150, 200), -1)
    cv2.circle(img3, (100, 100), 40, (200, 200, 100), -1)
    test_images.append(("复杂形状", img3))
    
    for name, img in test_images:
        print(f"\n测试 {name}:")
        features = learner.extract_features(img)
        print(f"  特征维度: {len(features)}")
        print(f"  特征范围: [{np.min(features):.3f}, {np.max(features):.3f}]")
        print(f"  非零特征数: {np.count_nonzero(features)}")
        print(f"  特征均值: {np.mean(features):.3f}")
        print(f"  特征标准差: {np.std(features):.3f}")

def test_recognition_with_real_images(learner, model_info):
    """使用真实图像测试识别"""
    print("\n" + "=" * 60)
    print("🎯 真实图像识别测试")
    print("=" * 60)
    
    if not (model_info['is_trained'] or model_info['model_exists']):
        print("❌ 模型未训练，无法进行识别测试")
        return
    
    # 查找工件图像
    images_dir = "./workpiece_data/images"
    if not os.path.exists(images_dir):
        print("❌ 工件图像目录不存在")
        return
    
    image_files = [f for f in os.listdir(images_dir) if f.endswith('.jpg')]
    print(f"找到 {len(image_files)} 个工件图像")
    
    # 测试前几个图像
    test_count = min(3, len(image_files))
    results = []
    
    for i in range(test_count):
        image_file = image_files[i]
        image_path = os.path.join(images_dir, image_file)
        
        print(f"\n测试图像: {image_file}")
        
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print("  ❌ 图像加载失败")
                continue
            
            print(f"  图像尺寸: {image.shape}")
            
            # 进行识别
            result = learner.recognize_workpiece(image)
            results.append((image_file, result))
            
            if result['success']:
                print(f"  ✅ 识别成功: {result['workpiece_name']}")
                print(f"  置信度: {result['confidence']:.3f}")
                print(f"  中心坐标: ({result['center_x']}, {result['center_y']})")
                if result['workpiece_info']:
                    info = result['workpiece_info']
                    print(f"  工件类型: {info.get('type', 'N/A')}")
                    print(f"  材料: {info.get('material', 'N/A')}")
            else:
                print(f"  ❌ 识别失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    return results

def analyze_confidence_issues(results):
    """分析置信度问题"""
    print("\n" + "=" * 60)
    print("📈 置信度问题分析")
    print("=" * 60)
    
    if not results:
        print("❌ 没有识别结果可分析")
        return
    
    successful_results = [r for _, r in results if r['success']]
    failed_results = [r for _, r in results if not r['success']]
    
    print(f"成功识别: {len(successful_results)} 个")
    print(f"识别失败: {len(failed_results)} 个")
    
    if successful_results:
        confidences = [r['confidence'] for r in successful_results]
        print(f"\n置信度统计:")
        print(f"  最高置信度: {max(confidences):.3f}")
        print(f"  最低置信度: {min(confidences):.3f}")
        print(f"  平均置信度: {np.mean(confidences):.3f}")
        print(f"  置信度标准差: {np.std(confidences):.3f}")
        
        # 分析置信度分布
        low_conf = [c for c in confidences if c < 0.5]
        med_conf = [c for c in confidences if 0.5 <= c < 0.7]
        high_conf = [c for c in confidences if c >= 0.7]
        
        print(f"\n置信度分布:")
        print(f"  低置信度 (<0.5): {len(low_conf)} 个")
        print(f"  中等置信度 (0.5-0.7): {len(med_conf)} 个")
        print(f"  高置信度 (>=0.7): {len(high_conf)} 个")
    
    if failed_results:
        print(f"\n失败原因分析:")
        error_types = {}
        for r in failed_results:
            error = r.get('error', '未知错误')
            error_types[error] = error_types.get(error, 0) + 1
        
        for error, count in error_types.items():
            print(f"  {error}: {count} 次")

def provide_optimization_suggestions(model_info, workpiece_list, results):
    """提供优化建议"""
    print("\n" + "=" * 60)
    print("💡 优化建议")
    print("=" * 60)
    
    suggestions = []
    
    # 检查工件数量
    if len(workpiece_list) < 3:
        suggestions.append("🔸 工件类型太少，建议增加更多不同类型的工件进行学习")
    
    # 检查学习样本数量
    total_samples = sum(wp['learn_count'] for wp in workpiece_list)
    avg_samples = total_samples / len(workpiece_list) if workpiece_list else 0
    
    if avg_samples < 3:
        suggestions.append("🔸 每个工件的学习样本太少，建议每个工件至少学习3-5个样本")
    
    # 检查识别结果
    if results:
        successful_results = [r for _, r in results if r['success']]
        if successful_results:
            confidences = [r['confidence'] for r in successful_results]
            avg_confidence = np.mean(confidences)
            
            if avg_confidence < 0.6:
                suggestions.append("🔸 平均置信度较低，建议:")
                suggestions.append("   - 增加更多训练样本")
                suggestions.append("   - 确保图像质量清晰")
                suggestions.append("   - 检查光照条件是否一致")
                suggestions.append("   - 降低置信度阈值到0.4-0.5")
            
            if avg_confidence < 0.8:
                suggestions.append("🔸 可以通过以下方式提高识别准确率:")
                suggestions.append("   - 使用更一致的拍照角度")
                suggestions.append("   - 保持工件在图像中的位置相对固定")
                suggestions.append("   - 减少背景干扰")
    
    # 特征提取优化
    if model_info['feature_type'] == 'combined':
        suggestions.append("🔸 当前使用组合特征，如果识别效果不佳，可以尝试:")
        suggestions.append("   - 单独使用SIFT特征 (适合纹理丰富的工件)")
        suggestions.append("   - 单独使用ORB特征 (计算速度更快)")
        suggestions.append("   - 单独使用LBP特征 (适合纹理分析)")
    
    # 模型训练建议
    suggestions.append("🔸 模型训练优化建议:")
    suggestions.append("   - 定期重新训练模型以包含新样本")
    suggestions.append("   - 确保不同工件之间有足够的特征差异")
    suggestions.append("   - 考虑删除质量较差的训练样本")
    
    # ROI使用建议
    suggestions.append("🔸 ROI使用建议:")
    suggestions.append("   - 使用ROI可以显著提高识别准确率")
    suggestions.append("   - ROI区域应该包含完整的工件")
    suggestions.append("   - 锁定ROI区域以保持一致性")
    
    if suggestions:
        for suggestion in suggestions:
            print(suggestion)
    else:
        print("✅ 当前系统配置良好，无需特别优化")

def main():
    """主函数"""
    print("🚀 视觉识别系统诊断分析")
    print("分析识别准确率低和置信度低的问题...")
    
    try:
        # 1. 分析模型状态
        learner, model_info = analyze_model_status()
        
        # 2. 分析工件数据库
        workpiece_list = analyze_workpiece_database(learner)
        
        # 3. 测试特征提取
        test_feature_extraction(learner)
        
        # 4. 测试真实图像识别
        results = test_recognition_with_real_images(learner, model_info)
        
        # 5. 分析置信度问题
        if results:
            analyze_confidence_issues(results)
        
        # 6. 提供优化建议
        provide_optimization_suggestions(model_info, workpiece_list, results)
        
        print("\n" + "=" * 60)
        print("✅ 分析完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
