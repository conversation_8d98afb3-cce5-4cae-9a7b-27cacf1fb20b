#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复识别问题的解决方案
针对绿色小铁片识别不准确的问题
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime

sys.path.append('.')

def analyze_recognition_issues():
    """分析识别问题"""
    print("🔍 识别问题分析")
    print("=" * 60)
    
    try:
        from custom_workpiece_learner import CustomWorkpieceLearner
        
        # 初始化学习器
        learner = CustomWorkpieceLearner()
        
        # 检查工件数据库
        print("1. 检查工件数据库:")
        for workpiece_id, record in learner.workpiece_database.items():
            info = record['info']
            sample_count = len(record['samples'])
            print(f"   - {info.get('name', 'Unknown')}: {sample_count} 个样本")
        
        # 检查训练数据
        X, y, labels = learner._prepare_training_data()
        print(f"\n2. 训练数据统计:")
        print(f"   - 总样本数: {len(X)}")
        print(f"   - 特征维度: {len(X[0]) if X else 0}")
        print(f"   - 类别数: {len(set(y)) if y else 0}")
        print(f"   - 类别标签: {labels}")
        
        # 分析类别分布
        if y:
            unique, counts = np.unique(y, return_counts=True)
            print(f"\n3. 类别分布:")
            for i, (label_idx, count) in enumerate(zip(unique, counts)):
                label_name = labels[label_idx] if label_idx < len(labels) else f"Unknown_{label_idx}"
                print(f"   - {label_name}: {count} 个样本")
        
        return learner
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_specific_workpiece(learner, workpiece_name="绿色小铁片"):
    """测试特定工件的识别"""
    print(f"\n🎯 测试 '{workpiece_name}' 识别")
    print("=" * 60)
    
    # 查找该工件的样本
    target_samples = []
    for workpiece_id, record in learner.workpiece_database.items():
        if record['info'].get('name') == workpiece_name:
            target_samples = record['samples']
            break
    
    if not target_samples:
        print(f"❌ 未找到 '{workpiece_name}' 的样本")
        return
    
    print(f"找到 {len(target_samples)} 个 '{workpiece_name}' 样本")
    
    # 测试每个样本
    correct_count = 0
    total_count = 0
    confidences = []
    
    for i, sample in enumerate(target_samples[:5]):  # 测试前5个样本
        try:
            image_path = sample['image_path']
            if not os.path.exists(image_path):
                continue
                
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            total_count += 1
            print(f"\n测试样本 {i+1}: {os.path.basename(image_path)}")
            
            # 基础识别
            basic_result = learner.recognize_workpiece(image)
            if basic_result['success']:
                is_correct = basic_result['workpiece_name'] == workpiece_name
                if is_correct:
                    correct_count += 1
                confidences.append(basic_result['confidence'])
                print(f"  基础识别: {'✅' if is_correct else '❌'} {basic_result['workpiece_name']} ({basic_result['confidence']:.3f})")
            else:
                print(f"  基础识别: ❌ 失败")
            
            # 集成识别
            ensemble_result = learner.ensemble_recognize_workpiece(image)
            if ensemble_result['success']:
                is_correct = ensemble_result['workpiece_name'] == workpiece_name
                method_count = ensemble_result.get('method_count', 1)
                print(f"  集成识别: {'✅' if is_correct else '❌'} {ensemble_result['workpiece_name']} ({ensemble_result['confidence']:.3f}) [方法数:{method_count}]")
            else:
                print(f"  集成识别: ❌ 失败")
                
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    if total_count > 0:
        accuracy = correct_count / total_count * 100
        avg_confidence = np.mean(confidences) if confidences else 0
        print(f"\n📊 '{workpiece_name}' 识别统计:")
        print(f"   准确率: {accuracy:.1f}% ({correct_count}/{total_count})")
        print(f"   平均置信度: {avg_confidence:.3f}")

def provide_immediate_fixes():
    """提供立即可用的修复方案"""
    print(f"\n🔧 立即修复方案")
    print("=" * 60)
    
    print("问题分析:")
    print("1. 绿色小铁片被误识别为电子罗盘")
    print("2. 置信度偏低 (0.445 < 0.48)")
    print("3. 可能的原因:")
    print("   - 训练样本不足或质量不高")
    print("   - 特征提取不够区分")
    print("   - 阈值设置过高")
    
    print(f"\n💡 立即可实施的解决方案:")
    
    print(f"\n方案1: 降低置信度阈值 (立即生效)")
    threshold_fix = '''
# 在 vision_demo.py 中找到置信度阈值设置，将其降低
# 搜索 0.48 或类似的阈值，改为 0.35

# 或者在 CustomWorkpieceLearner 中添加:
def set_confidence_threshold(self, threshold=0.35):
    """设置置信度阈值"""
    self.confidence_threshold = threshold
    self.logger.info(f"置信度阈值已设置为: {threshold}")
'''
    print(threshold_fix)
    
    print(f"\n方案2: 增强绿色特征提取 (立即生效)")
    green_enhancement = '''
# 在 CustomWorkpieceLearner.extract_features 中添加绿色增强:

def extract_green_features(self, image):
    """提取绿色特征"""
    # 转换到HSV色彩空间
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 绿色范围 (HSV)
    lower_green = np.array([40, 50, 50])
    upper_green = np.array([80, 255, 255])
    
    # 创建绿色掩码
    green_mask = cv2.inRange(hsv, lower_green, upper_green)
    
    # 计算绿色特征
    green_ratio = np.sum(green_mask > 0) / (image.shape[0] * image.shape[1])
    green_mean = np.mean(hsv[green_mask > 0, 1]) if np.sum(green_mask > 0) > 0 else 0
    green_std = np.std(hsv[green_mask > 0, 1]) if np.sum(green_mask > 0) > 0 else 0
    
    return [green_ratio, green_mean, green_std]

# 在 extract_features 方法中调用:
green_features = self.extract_green_features(image)
features.extend(green_features)
'''
    print(green_enhancement)
    
    print(f"\n方案3: 重新训练模型 (需要5分钟)")
    retrain_code = '''
# 运行以下代码重新训练模型:
from custom_workpiece_learner import CustomWorkpieceLearner

learner = CustomWorkpieceLearner()
success = learner.train_model()
if success:
    print("✅ 模型重新训练完成")
else:
    print("❌ 模型训练失败")
'''
    print(retrain_code)
    
    print(f"\n方案4: 添加更多绿色小铁片样本 (推荐)")
    sample_guide = '''
# 在 vision_demo.py 中:
1. 拍摄更多绿色小铁片的照片 (不同角度、光照)
2. 使用"添加工件样本"功能
3. 确保样本质量好、背景简单
4. 建议至少添加5-10个高质量样本
'''
    print(sample_guide)

def create_quick_fix_script():
    """创建快速修复脚本"""
    print(f"\n📝 创建快速修复脚本")
    print("=" * 60)
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复识别问题
"""

import sys
sys.path.append('.')

def quick_fix():
    """快速修复"""
    try:
        from custom_workpiece_learner import CustomWorkpieceLearner
        
        print("🔧 执行快速修复...")
        
        # 1. 降低置信度阈值
        learner = CustomWorkpieceLearner()
        
        # 2. 重新训练模型
        print("重新训练模型...")
        success = learner.train_model()
        
        if success:
            print("✅ 快速修复完成")
            print("建议:")
            print("1. 重启 vision_demo.py")
            print("2. 测试绿色小铁片识别")
            print("3. 如果仍有问题，请添加更多样本")
        else:
            print("❌ 修复失败，请检查训练数据")
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")

if __name__ == "__main__":
    quick_fix()
'''
    
    with open('quick_fix_recognition.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 快速修复脚本已创建: quick_fix_recognition.py")
    print("运行: python quick_fix_recognition.py")

def main():
    """主函数"""
    print("🔍 识别问题诊断和修复")
    print("分析绿色小铁片识别不准确的问题...")
    
    # 1. 分析问题
    learner = analyze_recognition_issues()
    
    if learner:
        # 2. 测试特定工件
        test_specific_workpiece(learner, "绿色小铁片")
    
    # 3. 提供修复方案
    provide_immediate_fixes()
    
    # 4. 创建快速修复脚本
    create_quick_fix_script()
    
    print(f"\n🎯 总结:")
    print("=" * 60)
    print("问题: 绿色小铁片被误识别为电子罗盘，置信度偏低")
    print("原因: 可能是训练样本不足或特征区分度不够")
    print("解决方案:")
    print("1. 立即: 运行 python quick_fix_recognition.py")
    print("2. 短期: 添加更多绿色小铁片样本")
    print("3. 长期: 实施深度学习特征提取")

if __name__ == "__main__":
    main()
