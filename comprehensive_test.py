#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试脚本 - 测试添加更多样本后的识别准确度和置信度
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime
sys.path.append('.')

from custom_workpiece_learner import CustomWorkpieceLearner

def analyze_updated_database():
    """分析更新后的工件数据库"""
    print("=" * 80)
    print("🔍 更新后的工件数据库分析")
    print("=" * 80)
    
    learner = CustomWorkpieceLearner()
    
    # 检查模型信息
    model_info = learner.get_model_info()
    print(f"模型已训练: {model_info['is_trained']}")
    print(f"模型文件存在: {model_info['model_exists']}")
    print(f"工件数量: {model_info['workpiece_count']}")
    print(f"特征类型: {model_info['feature_type']}")
    
    # 检查工件列表
    workpiece_list = learner.get_workpiece_list()
    print(f"\n总工件数量: {len(workpiece_list)}")
    
    # 统计每个工件的样本数量
    workpiece_stats = {}
    total_samples = 0
    
    for wp in workpiece_list:
        name = wp['name']
        learn_count = wp['learn_count']
        
        if name not in workpiece_stats:
            workpiece_stats[name] = {'count': 0, 'total_samples': 0}
        
        workpiece_stats[name]['count'] += 1
        workpiece_stats[name]['total_samples'] += learn_count
        total_samples += learn_count
    
    print(f"\n📊 工件类型统计:")
    for name, stats in workpiece_stats.items():
        avg_samples = stats['total_samples'] / stats['count']
        print(f"  {name}:")
        print(f"    - 工件实例数: {stats['count']}")
        print(f"    - 总样本数: {stats['total_samples']}")
        print(f"    - 平均样本数: {avg_samples:.1f}")
    
    print(f"\n总训练样本数: {total_samples}")
    print(f"平均每个工件样本数: {total_samples/len(workpiece_list):.1f}")
    
    return learner, model_info, workpiece_stats

def test_all_images_recognition(learner):
    """测试所有工件图像的识别效果"""
    print("\n" + "=" * 80)
    print("🎯 全面识别测试")
    print("=" * 80)
    
    images_dir = "./workpiece_data/images"
    if not os.path.exists(images_dir):
        print("❌ 工件图像目录不存在")
        return []
    
    # 获取所有图像文件
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 分类图像文件
    main_images = [f for f in image_files if "_sample_" not in os.path.basename(f)]
    sample_images = [f for f in image_files if "_sample_" in os.path.basename(f)]
    
    print(f"主要图像: {len(main_images)} 个")
    print(f"样本图像: {len(sample_images)} 个")
    
    # 测试结果存储
    results = []
    confidence_scores = []
    success_count = 0
    
    print(f"\n开始测试 {len(image_files)} 个图像...")
    
    for i, image_path in enumerate(image_files):
        image_name = os.path.basename(image_path)
        
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"  ❌ {image_name}: 图像加载失败")
                continue
            
            # 进行识别
            result = learner.recognize_workpiece(image)
            results.append((image_name, result))
            
            if result['success']:
                confidence = result['confidence']
                confidence_scores.append(confidence)
                success_count += 1
                
                # 根据置信度显示不同的状态
                if confidence >= 0.8:
                    status = "🟢 优秀"
                elif confidence >= 0.6:
                    status = "🟡 良好"
                elif confidence >= 0.4:
                    status = "🟠 一般"
                else:
                    status = "🔴 较差"
                
                print(f"  {status} {image_name}: {result['workpiece_name']} (置信度: {confidence:.3f})")
            else:
                print(f"  ❌ {image_name}: 识别失败 - {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"  ❌ {image_name}: 测试异常 - {e}")
    
    # 计算统计信息
    success_rate = success_count / len(image_files) * 100 if image_files else 0
    
    print(f"\n📊 识别统计:")
    print(f"  总测试图像: {len(image_files)}")
    print(f"  成功识别: {success_count}")
    print(f"  识别成功率: {success_rate:.1f}%")
    
    if confidence_scores:
        print(f"\n📈 置信度统计:")
        print(f"  平均置信度: {np.mean(confidence_scores):.3f}")
        print(f"  最高置信度: {np.max(confidence_scores):.3f}")
        print(f"  最低置信度: {np.min(confidence_scores):.3f}")
        print(f"  置信度标准差: {np.std(confidence_scores):.3f}")
        
        # 置信度分布分析
        excellent = len([c for c in confidence_scores if c >= 0.8])
        good = len([c for c in confidence_scores if 0.6 <= c < 0.8])
        fair = len([c for c in confidence_scores if 0.4 <= c < 0.6])
        poor = len([c for c in confidence_scores if c < 0.4])
        
        print(f"\n📊 置信度分布:")
        print(f"  优秀 (≥0.8): {excellent} 个 ({excellent/len(confidence_scores)*100:.1f}%)")
        print(f"  良好 (0.6-0.8): {good} 个 ({good/len(confidence_scores)*100:.1f}%)")
        print(f"  一般 (0.4-0.6): {fair} 个 ({fair/len(confidence_scores)*100:.1f}%)")
        print(f"  较差 (<0.4): {poor} 个 ({poor/len(confidence_scores)*100:.1f}%)")
    
    return results, confidence_scores

def analyze_workpiece_specific_performance(results):
    """分析每个工件类型的识别性能"""
    print("\n" + "=" * 80)
    print("📋 各工件类型性能分析")
    print("=" * 80)
    
    # 按工件类型分组结果
    workpiece_performance = {}
    
    for image_name, result in results:
        if result['success']:
            workpiece_name = result['workpiece_name']
            confidence = result['confidence']
            
            if workpiece_name not in workpiece_performance:
                workpiece_performance[workpiece_name] = []
            
            workpiece_performance[workpiece_name].append({
                'image': image_name,
                'confidence': confidence
            })
    
    # 分析每个工件类型的性能
    for workpiece_name, performances in workpiece_performance.items():
        confidences = [p['confidence'] for p in performances]
        
        print(f"\n🎯 {workpiece_name}:")
        print(f"  识别次数: {len(performances)}")
        print(f"  平均置信度: {np.mean(confidences):.3f}")
        print(f"  最高置信度: {np.max(confidences):.3f}")
        print(f"  最低置信度: {np.min(confidences):.3f}")
        print(f"  置信度标准差: {np.std(confidences):.3f}")
        
        # 显示置信度分布
        excellent = len([c for c in confidences if c >= 0.8])
        good = len([c for c in confidences if 0.6 <= c < 0.8])
        fair = len([c for c in confidences if 0.4 <= c < 0.6])
        poor = len([c for c in confidences if c < 0.4])
        
        print(f"  置信度分布: 优秀{excellent} 良好{good} 一般{fair} 较差{poor}")

def compare_with_previous_results():
    """与之前的结果进行对比"""
    print("\n" + "=" * 80)
    print("📊 性能对比分析")
    print("=" * 80)
    
    print("🔄 之前的性能 (样本不足时):")
    print("  平均置信度: 0.517")
    print("  置信度分布: 优秀0% 良好67% 一般0% 较差33%")
    print("  识别成功率: 100% (但样本量小)")
    
    print("\n🚀 当前性能将在测试完成后显示...")

def provide_improvement_analysis(confidence_scores, workpiece_stats):
    """提供改进效果分析"""
    print("\n" + "=" * 80)
    print("💡 改进效果分析")
    print("=" * 80)
    
    if not confidence_scores:
        print("❌ 没有有效的识别结果进行分析")
        return
    
    avg_confidence = np.mean(confidence_scores)
    previous_avg = 0.517  # 之前的平均置信度
    
    improvement = avg_confidence - previous_avg
    improvement_percent = (improvement / previous_avg) * 100
    
    print(f"📈 置信度改进:")
    print(f"  之前平均置信度: {previous_avg:.3f}")
    print(f"  当前平均置信度: {avg_confidence:.3f}")
    print(f"  绝对改进: {improvement:+.3f}")
    print(f"  相对改进: {improvement_percent:+.1f}%")
    
    # 分析样本数量的影响
    total_samples = sum(stats['total_samples'] for stats in workpiece_stats.values())
    avg_samples_per_workpiece = total_samples / len(workpiece_stats)
    
    print(f"\n📊 数据改进:")
    print(f"  之前平均样本数: 1.0 个/工件")
    print(f"  当前平均样本数: {avg_samples_per_workpiece:.1f} 个/工件")
    print(f"  样本数量提升: {avg_samples_per_workpiece:.1f}x")
    
    # 提供进一步优化建议
    print(f"\n🎯 进一步优化建议:")
    
    if avg_confidence < 0.7:
        print("  🔸 置信度仍有提升空间，建议:")
        print("    - 继续增加质量更高的训练样本")
        print("    - 确保样本的多样性（不同角度、光照）")
        print("    - 考虑重新训练模型")
    
    if avg_confidence >= 0.7:
        print("  ✅ 置信度已达到良好水平！")
        print("  🔸 可以考虑:")
        print("    - 微调置信度阈值以平衡准确率和召回率")
        print("    - 添加更多边缘案例样本")
    
    if avg_confidence >= 0.8:
        print("  🎉 置信度已达到优秀水平！")
        print("  🔸 建议:")
        print("    - 保持当前的数据质量")
        print("    - 定期更新和维护模型")

def main():
    """主函数"""
    print("🚀 全面测试更新后的识别系统")
    print("测试添加更多样本后的准确度和置信度...")
    print("=" * 80)
    
    try:
        # 1. 分析更新后的数据库
        learner, model_info, workpiece_stats = analyze_updated_database()
        
        # 2. 全面测试识别效果
        results, confidence_scores = test_all_images_recognition(learner)
        
        # 3. 分析各工件类型性能
        if results:
            analyze_workpiece_specific_performance(results)
        
        # 4. 对比之前的结果
        compare_with_previous_results()
        
        # 5. 提供改进效果分析
        provide_improvement_analysis(confidence_scores, workpiece_stats)
        
        print("\n" + "=" * 80)
        print("✅ 全面测试完成")
        print("=" * 80)
        
        # 保存测试结果
        test_report = {
            'timestamp': datetime.now().isoformat(),
            'model_info': model_info,
            'workpiece_stats': workpiece_stats,
            'total_images_tested': len(results),
            'successful_recognitions': len(confidence_scores),
            'success_rate': len(confidence_scores) / len(results) * 100 if results else 0,
            'confidence_stats': {
                'mean': float(np.mean(confidence_scores)) if confidence_scores else 0,
                'std': float(np.std(confidence_scores)) if confidence_scores else 0,
                'min': float(np.min(confidence_scores)) if confidence_scores else 0,
                'max': float(np.max(confidence_scores)) if confidence_scores else 0
            } if confidence_scores else {}
        }
        
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 测试报告已保存到: test_report.json")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
