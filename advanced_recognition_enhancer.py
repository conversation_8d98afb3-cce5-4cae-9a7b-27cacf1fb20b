#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级识别准确率提升器
在现有增强系统基础上，实施更高级的识别准确率提升技术
"""

import cv2
import numpy as np
import json
import os
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA, FastICA
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.model_selection import cross_val_score, GridSearchCV
from sklearn.calibration import CalibratedClassifierCV
import joblib
import xgboost as xgb
from scipy import ndimage
from skimage import feature, filters, segmentation, measure
from skimage.feature import local_binary_pattern, greycomatrix, greycoprops
import warnings
warnings.filterwarnings('ignore')

class AdvancedRecognitionEnhancer:
    """高级识别准确率提升器"""
    
    def __init__(self, base_learner=None):
        """初始化高级增强器"""
        self.base_learner = base_learner
        self.logger = self._setup_logger()
        
        # 高级特征提取器
        self.advanced_features = {
            'texture_features': True,
            'geometric_features': True,
            'frequency_features': True,
            'statistical_features': True,
            'deep_features': True
        }
        
        # 高级数据增强
        self.augmentation_methods = [
            'rotation_invariant',
            'perspective_transform',
            'elastic_deformation',
            'lighting_variation',
            'noise_injection'
        ]
        
        # 集成学习模型
        self.ensemble_models = {}
        self.meta_learner = None
        self.is_advanced_trained = False
        
        # 自适应阈值系统
        self.adaptive_thresholds = {}
        self.confidence_calibrator = None
        
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger('AdvancedRecognitionEnhancer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_advanced_texture_features(self, image: np.ndarray) -> np.ndarray:
        """提取高级纹理特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        features = []
        
        # 1. 改进的LBP特征
        radius = 3
        n_points = 8 * radius
        lbp = local_binary_pattern(gray, n_points, radius, method='uniform')
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, range=(0, n_points + 2))
        lbp_hist = lbp_hist.astype(float)
        lbp_hist /= (lbp_hist.sum() + 1e-7)
        features.extend(lbp_hist)
        
        # 2. 灰度共生矩阵特征 (GLCM)
        distances = [1, 2, 3]
        angles = [0, 45, 90, 135]
        
        for distance in distances:
            for angle in angles:
                glcm = greycomatrix(gray, [distance], [np.radians(angle)], 
                                 levels=256, symmetric=True, normed=True)
                
                # 提取GLCM属性
                contrast = greycoprops(glcm, 'contrast')[0, 0]
                dissimilarity = greycoprops(glcm, 'dissimilarity')[0, 0]
                homogeneity = greycoprops(glcm, 'homogeneity')[0, 0]
                energy = greycoprops(glcm, 'energy')[0, 0]
                correlation = greycoprops(glcm, 'correlation')[0, 0]
                
                features.extend([contrast, dissimilarity, homogeneity, energy, correlation])
        
        # 3. Gabor滤波器特征
        gabor_responses = []
        for theta in range(0, 180, 30):  # 6个方向
            for frequency in [0.1, 0.3, 0.5]:  # 3个频率
                real, _ = filters.gabor(gray, frequency=frequency, theta=np.radians(theta))
                gabor_responses.append(real)
        
        # 统计Gabor响应
        for response in gabor_responses:
            features.extend([
                np.mean(response),
                np.std(response),
                np.max(response),
                np.min(response)
            ])
        
        return np.array(features, dtype=np.float32)
    
    def extract_advanced_geometric_features(self, image: np.ndarray) -> np.ndarray:
        """提取高级几何特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        features = []
        
        # 1. 边缘检测和轮廓分析
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # 找到最大轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            
            # 基本几何特征
            area = cv2.contourArea(largest_contour)
            perimeter = cv2.arcLength(largest_contour, True)
            
            # 边界框特征
            x, y, w, h = cv2.boundingRect(largest_contour)
            aspect_ratio = w / h if h > 0 else 0
            extent = area / (w * h) if w * h > 0 else 0
            
            # 凸包特征
            hull = cv2.convexHull(largest_contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0
            
            # 椭圆拟合特征
            if len(largest_contour) >= 5:
                ellipse = cv2.fitEllipse(largest_contour)
                (center_x, center_y), (major_axis, minor_axis), angle = ellipse
                eccentricity = np.sqrt(1 - (minor_axis/major_axis)**2) if major_axis > 0 else 0
                features.extend([major_axis, minor_axis, eccentricity, angle])
            else:
                features.extend([0, 0, 0, 0])
            
            # 矩特征
            moments = cv2.moments(largest_contour)
            if moments['m00'] != 0:
                hu_moments = cv2.HuMoments(moments).flatten()
                # 对数变换使特征更稳定
                hu_moments = -np.sign(hu_moments) * np.log10(np.abs(hu_moments) + 1e-10)
                features.extend(hu_moments)
            else:
                features.extend([0] * 7)
            
            features.extend([area, perimeter, aspect_ratio, extent, solidity])
        else:
            # 没有找到轮廓时的默认值
            features.extend([0] * 16)
        
        # 2. 区域属性
        labeled_img = measure.label(gray > filters.threshold_otsu(gray))
        regions = measure.regionprops(labeled_img)
        
        if regions:
            largest_region = max(regions, key=lambda r: r.area)
            
            # 区域几何特征
            features.extend([
                largest_region.area,
                largest_region.perimeter,
                largest_region.eccentricity,
                largest_region.orientation,
                largest_region.major_axis_length,
                largest_region.minor_axis_length,
                largest_region.equivalent_diameter
            ])
        else:
            features.extend([0] * 7)
        
        return np.array(features, dtype=np.float32)
    
    def extract_frequency_domain_features(self, image: np.ndarray) -> np.ndarray:
        """提取频域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        features = []
        
        # 1. 傅里叶变换特征
        f_transform = np.fft.fft2(gray)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = np.abs(f_shift)
        
        # 频谱统计特征
        features.extend([
            np.mean(magnitude_spectrum),
            np.std(magnitude_spectrum),
            np.max(magnitude_spectrum),
            np.min(magnitude_spectrum)
        ])
        
        # 2. 小波变换特征
        try:
            import pywt
            coeffs = pywt.dwt2(gray, 'db4')
            cA, (cH, cV, cD) = coeffs
            
            # 各子带的统计特征
            for coeff in [cA, cH, cV, cD]:
                features.extend([
                    np.mean(coeff),
                    np.std(coeff),
                    np.var(coeff)
                ])
        except ImportError:
            # 如果没有pywt，使用简单的频域分析
            features.extend([0] * 12)
        
        # 3. DCT特征
        dct = cv2.dct(np.float32(gray))
        
        # 取DCT系数的统计特征
        features.extend([
            np.mean(dct),
            np.std(dct),
            np.sum(np.abs(dct[:8, :8]))  # 低频成分
        ])
        
        return np.array(features, dtype=np.float32)
    
    def extract_statistical_features(self, image: np.ndarray) -> np.ndarray:
        """提取统计特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        features = []
        
        # 1. 基本统计特征
        features.extend([
            np.mean(gray),
            np.std(gray),
            np.var(gray),
            np.min(gray),
            np.max(gray),
            np.median(gray)
        ])
        
        # 2. 高阶矩
        from scipy import stats
        features.extend([
            stats.skew(gray.flatten()),
            stats.kurtosis(gray.flatten())
        ])
        
        # 3. 分位数特征
        percentiles = [10, 25, 75, 90]
        for p in percentiles:
            features.append(np.percentile(gray, p))
        
        # 4. 梯度统计
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        features.extend([
            np.mean(gradient_magnitude),
            np.std(gradient_magnitude),
            np.max(gradient_magnitude)
        ])
        
        return np.array(features, dtype=np.float32)
    
    def extract_all_advanced_features(self, image: np.ndarray) -> np.ndarray:
        """提取所有高级特征"""
        all_features = []
        
        try:
            # 纹理特征
            if self.advanced_features['texture_features']:
                texture_feat = self.extract_advanced_texture_features(image)
                all_features.extend(texture_feat)
            
            # 几何特征
            if self.advanced_features['geometric_features']:
                geometric_feat = self.extract_advanced_geometric_features(image)
                all_features.extend(geometric_feat)
            
            # 频域特征
            if self.advanced_features['frequency_features']:
                frequency_feat = self.extract_frequency_domain_features(image)
                all_features.extend(frequency_feat)
            
            # 统计特征
            if self.advanced_features['statistical_features']:
                statistical_feat = self.extract_statistical_features(image)
                all_features.extend(statistical_feat)
            
            # 原始特征 (如果有基础学习器)
            if self.base_learner:
                original_feat = self.base_learner.extract_features(image)
                all_features.extend(original_feat)
            
        except Exception as e:
            self.logger.error(f"高级特征提取失败: {e}")
            return np.array([])
        
        return np.array(all_features, dtype=np.float32)

    def advanced_data_augmentation(self, image: np.ndarray, num_augmentations: int = 5) -> List[np.ndarray]:
        """高级数据增强"""
        augmented_images = [image.copy()]  # 包含原始图像

        try:
            h, w = image.shape[:2]

            for i in range(num_augmentations):
                aug_image = image.copy()

                # 1. 旋转不变性增强
                if 'rotation_invariant' in self.augmentation_methods:
                    angle = np.random.uniform(-15, 15)
                    M = cv2.getRotationMatrix2D((w//2, h//2), angle, 1.0)
                    aug_image = cv2.warpAffine(aug_image, M, (w, h))

                # 2. 透视变换
                if 'perspective_transform' in self.augmentation_methods:
                    pts1 = np.float32([[0, 0], [w, 0], [0, h], [w, h]])
                    # 轻微的透视变换
                    offset = min(w, h) * 0.05
                    pts2 = pts1 + np.random.uniform(-offset, offset, pts1.shape)
                    M = cv2.getPerspectiveTransform(pts1, pts2)
                    aug_image = cv2.warpPerspective(aug_image, M, (w, h))

                # 3. 弹性变形
                if 'elastic_deformation' in self.augmentation_methods:
                    # 简化的弹性变形
                    dx = np.random.uniform(-2, 2, (h, w)).astype(np.float32)
                    dy = np.random.uniform(-2, 2, (h, w)).astype(np.float32)

                    x, y = np.meshgrid(np.arange(w), np.arange(h))
                    map_x = (x + dx).astype(np.float32)
                    map_y = (y + dy).astype(np.float32)

                    aug_image = cv2.remap(aug_image, map_x, map_y, cv2.INTER_LINEAR)

                # 4. 光照变化
                if 'lighting_variation' in self.augmentation_methods:
                    # 亮度调整
                    brightness = np.random.uniform(0.8, 1.2)
                    aug_image = cv2.convertScaleAbs(aug_image, alpha=brightness, beta=0)

                    # 对比度调整
                    contrast = np.random.uniform(0.8, 1.2)
                    aug_image = cv2.convertScaleAbs(aug_image, alpha=contrast, beta=0)

                # 5. 噪声注入
                if 'noise_injection' in self.augmentation_methods:
                    noise = np.random.normal(0, 5, aug_image.shape).astype(np.uint8)
                    aug_image = cv2.add(aug_image, noise)

                augmented_images.append(aug_image)

        except Exception as e:
            self.logger.warning(f"数据增强失败: {e}")

        return augmented_images

    def train_advanced_ensemble(self, X, y, labels):
        """训练高级集成模型"""
        try:
            self.logger.info("开始训练高级集成模型...")

            # 数据预处理
            scaler = RobustScaler()  # 使用更鲁棒的缩放器
            X_scaled = scaler.fit_transform(X)

            # 特征选择
            selector = SelectKBest(f_classif, k=min(500, X.shape[1]))
            X_selected = selector.fit_transform(X_scaled, y)

            self.logger.info(f"特征选择: {X.shape[1]} → {X_selected.shape[1]}")

            # 定义高级模型
            models = {
                'xgboost': xgb.XGBClassifier(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    eval_metric='mlogloss'
                ),
                'gradient_boosting': GradientBoostingClassifier(
                    n_estimators=200,
                    max_depth=5,
                    learning_rate=0.1,
                    random_state=42
                ),
                'random_forest': RandomForestClassifier(
                    n_estimators=300,
                    max_depth=20,
                    min_samples_split=2,
                    random_state=42
                ),
                'svm_rbf': SVC(
                    kernel='rbf',
                    C=10.0,
                    gamma='scale',
                    probability=True,
                    random_state=42
                ),
                'mlp': MLPClassifier(
                    hidden_layer_sizes=(200, 100, 50),
                    max_iter=1000,
                    random_state=42
                )
            }

            # 训练各个模型
            trained_models = {}
            model_scores = {}

            for name, model in models.items():
                try:
                    self.logger.info(f"训练 {name} 模型...")
                    model.fit(X_selected, y)

                    # 交叉验证评估
                    scores = cross_val_score(model, X_selected, y, cv=3, scoring='accuracy')
                    mean_score = scores.mean()

                    trained_models[name] = model
                    model_scores[name] = mean_score

                    self.logger.info(f"{name} 交叉验证准确率: {mean_score:.3f} ± {scores.std():.3f}")

                except Exception as e:
                    self.logger.warning(f"{name} 模型训练失败: {e}")

            # 保存训练好的模型和预处理器
            self.ensemble_models = trained_models
            self.scaler = scaler
            self.feature_selector = selector
            self.model_scores = model_scores

            # 训练元学习器 (Stacking)
            self._train_meta_learner(X_selected, y)

            # 训练置信度校准器
            self._train_confidence_calibrator(X_selected, y)

            self.is_advanced_trained = True
            self.logger.info("高级集成模型训练完成")

            return True

        except Exception as e:
            self.logger.error(f"高级集成模型训练失败: {e}")
            return False

    def _train_meta_learner(self, X, y):
        """训练元学习器"""
        try:
            # 生成元特征 (各个基模型的预测概率)
            meta_features = []

            for name, model in self.ensemble_models.items():
                try:
                    proba = model.predict_proba(X)
                    meta_features.append(proba)
                except:
                    # 如果模型不支持概率预测，使用决策函数
                    pred = model.predict(X).reshape(-1, 1)
                    meta_features.append(pred)

            if meta_features:
                X_meta = np.hstack(meta_features)

                # 使用简单的逻辑回归作为元学习器
                from sklearn.linear_model import LogisticRegression
                self.meta_learner = LogisticRegression(random_state=42, max_iter=1000)
                self.meta_learner.fit(X_meta, y)

                self.logger.info("元学习器训练完成")

        except Exception as e:
            self.logger.warning(f"元学习器训练失败: {e}")

    def _train_confidence_calibrator(self, X, y):
        """训练置信度校准器"""
        try:
            # 选择最佳模型进行校准
            if self.model_scores:
                best_model_name = max(self.model_scores.keys(), key=lambda k: self.model_scores[k])
                best_model = self.ensemble_models[best_model_name]

                # 使用Platt缩放进行置信度校准
                self.confidence_calibrator = CalibratedClassifierCV(
                    best_model, method='platt', cv=3
                )
                self.confidence_calibrator.fit(X, y)

                self.logger.info(f"置信度校准器训练完成 (基于 {best_model_name})")

        except Exception as e:
            self.logger.warning(f"置信度校准器训练失败: {e}")

    def advanced_predict(self, image: np.ndarray) -> Dict:
        """高级预测"""
        try:
            if not self.is_advanced_trained:
                return {'success': False, 'error': '高级模型未训练'}

            # 提取高级特征
            features = self.extract_all_advanced_features(image)
            if len(features) == 0:
                return {'success': False, 'error': '特征提取失败'}

            # 预处理
            features_scaled = self.scaler.transform([features])
            features_selected = self.feature_selector.transform(features_scaled)

            # 各个模型的预测
            predictions = {}
            probabilities = {}

            for name, model in self.ensemble_models.items():
                try:
                    pred = model.predict(features_selected)[0]
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(features_selected)[0]
                        confidence = np.max(proba)
                    else:
                        confidence = 0.5  # 默认置信度

                    predictions[name] = pred
                    probabilities[name] = confidence

                except Exception as e:
                    self.logger.warning(f"{name} 预测失败: {e}")

            # 集成预测
            if predictions:
                # 加权投票
                weighted_votes = {}
                for name, pred in predictions.items():
                    weight = self.model_scores.get(name, 0.5)
                    confidence = probabilities.get(name, 0.5)

                    if pred not in weighted_votes:
                        weighted_votes[pred] = 0
                    weighted_votes[pred] += weight * confidence

                # 选择得票最高的预测
                final_prediction = max(weighted_votes.keys(), key=lambda k: weighted_votes[k])
                final_confidence = weighted_votes[final_prediction] / sum(weighted_votes.values())

                # 使用校准器调整置信度
                if self.confidence_calibrator:
                    try:
                        calibrated_proba = self.confidence_calibrator.predict_proba(features_selected)[0]
                        calibrated_confidence = np.max(calibrated_proba)
                        final_confidence = (final_confidence + calibrated_confidence) / 2
                    except:
                        pass

                return {
                    'success': True,
                    'prediction': int(final_prediction),
                    'confidence': float(final_confidence),
                    'individual_predictions': predictions,
                    'individual_confidences': probabilities,
                    'ensemble_method': 'weighted_voting',
                    'calibrated': self.confidence_calibrator is not None
                }

            return {'success': False, 'error': '所有模型预测失败'}

        except Exception as e:
            self.logger.error(f"高级预测失败: {e}")
            return {'success': False, 'error': str(e)}
