#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的识别系统
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime

sys.path.append('.')
from optimized_workpiece_learner import OptimizedWorkpieceLearner
from custom_workpiece_learner import CustomWorkpieceLearner

def compare_systems():
    """对比原始系统和优化系统"""
    print("🚀 对比原始系统和优化系统")
    print("=" * 80)
    
    # 初始化两个系统
    original_learner = CustomWorkpieceLearner()
    optimized_learner = OptimizedWorkpieceLearner()
    
    print("📊 系统配置对比:")
    print(f"原始系统 - 特征维度: 8357")
    
    opt_info = optimized_learner.get_optimization_info()
    print(f"优化系统 - 特征维度: {opt_info['selected_features_count']}")
    print(f"优化系统 - 特征选择: {'启用' if opt_info['feature_selection_enabled'] else '禁用'}")
    print(f"优化系统 - 默认阈值: {opt_info['default_threshold']}")
    print(f"优化系统 - 动态阈值: {opt_info['dynamic_thresholds']}")
    
    return original_learner, optimized_learner

def train_optimized_system(optimized_learner):
    """训练优化系统"""
    print("\n" + "=" * 80)
    print("🔧 训练优化系统")
    print("=" * 80)
    
    print("开始训练优化模型...")
    success = optimized_learner.train_optimized_model()
    
    if success:
        print("✅ 优化模型训练成功")
        return True
    else:
        print("❌ 优化模型训练失败")
        return False

def test_recognition_performance(original_learner, optimized_learner):
    """测试识别性能"""
    print("\n" + "=" * 80)
    print("🎯 识别性能测试")
    print("=" * 80)
    
    # 获取测试图像
    images_dir = "./workpiece_data/images"
    if not os.path.exists(images_dir):
        print("❌ 图像目录不存在")
        return
    
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    print(f"找到 {len(image_files)} 个测试图像")
    
    # 测试结果存储
    original_results = []
    optimized_results = []
    
    print(f"\n开始对比测试...")
    
    for i, image_path in enumerate(image_files[:10]):  # 测试前10个图像
        image_name = os.path.basename(image_path)
        
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            print(f"\n📷 测试图像: {image_name}")
            
            # 原始系统识别
            try:
                original_result = original_learner.recognize_workpiece(image)
                original_results.append((image_name, original_result))
                
                if original_result['success']:
                    print(f"  原始系统: {original_result['workpiece_name']} (置信度: {original_result['confidence']:.3f})")
                else:
                    print(f"  原始系统: 识别失败 - {original_result.get('error', '未知错误')}")
            except Exception as e:
                print(f"  原始系统: 测试异常 - {e}")
                original_results.append((image_name, {'success': False, 'error': str(e)}))
            
            # 优化系统识别
            try:
                optimized_result = optimized_learner.recognize_workpiece_optimized(image)
                optimized_results.append((image_name, optimized_result))
                
                if optimized_result['success']:
                    print(f"  优化系统: {optimized_result['workpiece_name']} (置信度: {optimized_result['confidence']:.3f}, 阈值: {optimized_result.get('threshold', 'N/A')})")
                else:
                    print(f"  优化系统: 识别失败 - {optimized_result.get('error', '未知错误')}")
            except Exception as e:
                print(f"  优化系统: 测试异常 - {e}")
                optimized_results.append((image_name, {'success': False, 'error': str(e)}))
                
        except Exception as e:
            print(f"  ❌ 图像处理异常: {e}")
    
    return original_results, optimized_results

def analyze_performance_comparison(original_results, optimized_results):
    """分析性能对比"""
    print("\n" + "=" * 80)
    print("📊 性能对比分析")
    print("=" * 80)
    
    # 统计成功率
    original_success = len([r for _, r in original_results if r['success']])
    optimized_success = len([r for _, r in optimized_results if r['success']])
    
    total_tests = len(original_results)
    
    print(f"📈 识别成功率对比:")
    print(f"  原始系统: {original_success}/{total_tests} ({original_success/total_tests*100:.1f}%)")
    print(f"  优化系统: {optimized_success}/{total_tests} ({optimized_success/total_tests*100:.1f}%)")
    
    # 统计置信度
    original_confidences = [r['confidence'] for _, r in original_results if r['success']]
    optimized_confidences = [r['confidence'] for _, r in optimized_results if r['success']]
    
    if original_confidences:
        print(f"\n📊 原始系统置信度统计:")
        print(f"  平均置信度: {np.mean(original_confidences):.3f}")
        print(f"  最高置信度: {np.max(original_confidences):.3f}")
        print(f"  最低置信度: {np.min(original_confidences):.3f}")
        print(f"  标准差: {np.std(original_confidences):.3f}")
    
    if optimized_confidences:
        print(f"\n📊 优化系统置信度统计:")
        print(f"  平均置信度: {np.mean(optimized_confidences):.3f}")
        print(f"  最高置信度: {np.max(optimized_confidences):.3f}")
        print(f"  最低置信度: {np.min(optimized_confidences):.3f}")
        print(f"  标准差: {np.std(optimized_confidences):.3f}")
    
    # 计算改进效果
    if original_confidences and optimized_confidences:
        confidence_improvement = np.mean(optimized_confidences) - np.mean(original_confidences)
        improvement_percent = (confidence_improvement / np.mean(original_confidences)) * 100
        
        print(f"\n🎯 改进效果:")
        print(f"  置信度提升: {confidence_improvement:+.3f}")
        print(f"  相对提升: {improvement_percent:+.1f}%")
        
        # 置信度分布对比
        original_excellent = len([c for c in original_confidences if c >= 0.8])
        original_good = len([c for c in original_confidences if 0.6 <= c < 0.8])
        original_fair = len([c for c in original_confidences if 0.4 <= c < 0.6])
        original_poor = len([c for c in original_confidences if c < 0.4])
        
        optimized_excellent = len([c for c in optimized_confidences if c >= 0.8])
        optimized_good = len([c for c in optimized_confidences if 0.6 <= c < 0.8])
        optimized_fair = len([c for c in optimized_confidences if 0.4 <= c < 0.6])
        optimized_poor = len([c for c in optimized_confidences if c < 0.4])
        
        print(f"\n📊 置信度分布对比:")
        print(f"  优秀 (≥0.8): 原始{original_excellent} → 优化{optimized_excellent}")
        print(f"  良好 (0.6-0.8): 原始{original_good} → 优化{optimized_good}")
        print(f"  一般 (0.4-0.6): 原始{original_fair} → 优化{optimized_fair}")
        print(f"  较差 (<0.4): 原始{original_poor} → 优化{optimized_poor}")
    
    # 保存对比结果
    comparison_result = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': total_tests,
        'original_system': {
            'success_count': original_success,
            'success_rate': original_success/total_tests*100,
            'confidences': original_confidences
        },
        'optimized_system': {
            'success_count': optimized_success,
            'success_rate': optimized_success/total_tests*100,
            'confidences': optimized_confidences
        }
    }
    
    with open('system_comparison_result.json', 'w', encoding='utf-8') as f:
        json.dump(comparison_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 对比结果已保存到: system_comparison_result.json")

def main():
    """主函数"""
    print("🚀 优化系统测试")
    print("测试特征工程优化、动态阈值调整和模型优化的效果...")
    
    try:
        # 1. 对比系统配置
        original_learner, optimized_learner = compare_systems()
        
        # 2. 训练优化系统
        if not train_optimized_system(optimized_learner):
            print("❌ 优化系统训练失败，无法继续测试")
            return
        
        # 3. 测试识别性能
        original_results, optimized_results = test_recognition_performance(original_learner, optimized_learner)
        
        # 4. 分析性能对比
        analyze_performance_comparison(original_results, optimized_results)
        
        print("\n" + "=" * 80)
        print("✅ 优化系统测试完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
