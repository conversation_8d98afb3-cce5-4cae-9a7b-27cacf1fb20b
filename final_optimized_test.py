#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化系统测试
使用优化后的特征工程、动态阈值和模型参数
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime

sys.path.append('.')
from optimized_workpiece_learner import OptimizedWorkpieceLearner
from custom_workpiece_learner import CustomWorkpieceLearner

def final_comparison_test():
    """最终对比测试"""
    print("🚀 最终优化系统对比测试")
    print("=" * 80)
    
    # 初始化系统
    print("初始化系统...")
    original_learner = CustomWorkpieceLearner()
    optimized_learner = OptimizedWorkpieceLearner()
    
    # 确保优化系统已训练
    if not optimized_learner.is_trained:
        print("训练优化系统...")
        success = optimized_learner.train_optimized_model()
        if not success:
            print("❌ 优化系统训练失败")
            return
    
    # 获取测试图像
    images_dir = "./workpiece_data/images"
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    print(f"找到 {len(image_files)} 个测试图像")
    
    # 测试结果
    original_results = []
    optimized_results = []
    
    print(f"\n开始对比测试...")
    
    for i, image_path in enumerate(image_files):
        image_name = os.path.basename(image_path)
        
        try:
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            print(f"\n📷 测试 {i+1}/{len(image_files)}: {image_name}")
            
            # 原始系统测试
            try:
                original_result = original_learner.recognize_workpiece(image)
                original_results.append((image_name, original_result))
                
                if original_result['success']:
                    print(f"  原始: ✅ {original_result['workpiece_name']} ({original_result['confidence']:.3f})")
                else:
                    print(f"  原始: ❌ {original_result.get('error', '失败')}")
            except Exception as e:
                print(f"  原始: ❌ 异常 - {e}")
                original_results.append((image_name, {'success': False, 'error': str(e)}))
            
            # 优化系统测试
            try:
                optimized_result = optimized_learner.recognize_workpiece_optimized(image)
                optimized_results.append((image_name, optimized_result))
                
                if optimized_result['success']:
                    threshold = optimized_result.get('threshold', 'N/A')
                    print(f"  优化: ✅ {optimized_result['workpiece_name']} ({optimized_result['confidence']:.3f}, 阈值:{threshold:.3f})")
                else:
                    print(f"  优化: ❌ {optimized_result.get('error', '失败')}")
            except Exception as e:
                print(f"  优化: ❌ 异常 - {e}")
                optimized_results.append((image_name, {'success': False, 'error': str(e)}))
                
        except Exception as e:
            print(f"  ❌ 图像处理异常: {e}")
    
    return original_results, optimized_results

def analyze_final_results(original_results, optimized_results):
    """分析最终结果"""
    print("\n" + "=" * 80)
    print("📊 最终性能分析")
    print("=" * 80)
    
    # 基本统计
    total_tests = len(original_results)
    original_success = len([r for _, r in original_results if r['success']])
    optimized_success = len([r for _, r in optimized_results if r['success']])
    
    print(f"📈 识别成功率对比:")
    print(f"  测试图像总数: {total_tests}")
    print(f"  原始系统成功: {original_success} ({original_success/total_tests*100:.1f}%)")
    print(f"  优化系统成功: {optimized_success} ({optimized_success/total_tests*100:.1f}%)")
    print(f"  成功率提升: {(optimized_success-original_success)/total_tests*100:+.1f}%")
    
    # 置信度分析
    original_confidences = [r['confidence'] for _, r in original_results if r['success']]
    optimized_confidences = [r['confidence'] for _, r in optimized_results if r['success']]
    
    if original_confidences and optimized_confidences:
        print(f"\n📊 置信度对比:")
        print(f"  原始系统:")
        print(f"    平均置信度: {np.mean(original_confidences):.3f}")
        print(f"    最高置信度: {np.max(original_confidences):.3f}")
        print(f"    最低置信度: {np.min(original_confidences):.3f}")
        print(f"    标准差: {np.std(original_confidences):.3f}")
        
        print(f"  优化系统:")
        print(f"    平均置信度: {np.mean(optimized_confidences):.3f}")
        print(f"    最高置信度: {np.max(optimized_confidences):.3f}")
        print(f"    最低置信度: {np.min(optimized_confidences):.3f}")
        print(f"    标准差: {np.std(optimized_confidences):.3f}")
        
        # 计算改进
        confidence_improvement = np.mean(optimized_confidences) - np.mean(original_confidences)
        improvement_percent = (confidence_improvement / np.mean(original_confidences)) * 100
        
        print(f"\n🎯 置信度改进:")
        print(f"  绝对改进: {confidence_improvement:+.3f}")
        print(f"  相对改进: {improvement_percent:+.1f}%")
        
        # 置信度分布对比
        def analyze_distribution(confidences, name):
            excellent = len([c for c in confidences if c >= 0.8])
            good = len([c for c in confidences if 0.6 <= c < 0.8])
            fair = len([c for c in confidences if 0.4 <= c < 0.6])
            poor = len([c for c in confidences if c < 0.4])
            total = len(confidences)
            
            print(f"  {name}:")
            print(f"    优秀 (≥0.8): {excellent} ({excellent/total*100:.1f}%)")
            print(f"    良好 (0.6-0.8): {good} ({good/total*100:.1f}%)")
            print(f"    一般 (0.4-0.6): {fair} ({fair/total*100:.1f}%)")
            print(f"    较差 (<0.4): {poor} ({poor/total*100:.1f}%)")
            
            return excellent, good, fair, poor
        
        print(f"\n📊 置信度分布对比:")
        orig_excellent, orig_good, orig_fair, orig_poor = analyze_distribution(original_confidences, "原始系统")
        opt_excellent, opt_good, opt_fair, opt_poor = analyze_distribution(optimized_confidences, "优化系统")
        
        print(f"\n📈 分布改进:")
        print(f"  优秀级别: {orig_excellent} → {opt_excellent} ({opt_excellent-orig_excellent:+d})")
        print(f"  良好级别: {orig_good} → {opt_good} ({opt_good-orig_good:+d})")
        print(f"  一般级别: {orig_fair} → {opt_fair} ({opt_fair-orig_fair:+d})")
        print(f"  较差级别: {orig_poor} → {opt_poor} ({opt_poor-orig_poor:+d})")
    
    # 各工件类型分析
    print(f"\n🎯 各工件类型性能对比:")
    
    # 统计各类型结果
    original_by_class = {}
    optimized_by_class = {}
    
    for _, result in original_results:
        if result['success']:
            name = result['workpiece_name']
            if name not in original_by_class:
                original_by_class[name] = []
            original_by_class[name].append(result['confidence'])
    
    for _, result in optimized_results:
        if result['success']:
            name = result['workpiece_name']
            if name not in optimized_by_class:
                optimized_by_class[name] = []
            optimized_by_class[name].append(result['confidence'])
    
    all_classes = set(list(original_by_class.keys()) + list(optimized_by_class.keys()))
    
    for class_name in all_classes:
        orig_confs = original_by_class.get(class_name, [])
        opt_confs = optimized_by_class.get(class_name, [])
        
        print(f"\n  📋 {class_name}:")
        print(f"    原始: {len(orig_confs)} 次识别, 平均置信度 {np.mean(orig_confs):.3f}" if orig_confs else "    原始: 无识别结果")
        print(f"    优化: {len(opt_confs)} 次识别, 平均置信度 {np.mean(opt_confs):.3f}" if opt_confs else "    优化: 无识别结果")
        
        if orig_confs and opt_confs:
            improvement = np.mean(opt_confs) - np.mean(orig_confs)
            print(f"    改进: {improvement:+.3f}")
    
    return {
        'total_tests': total_tests,
        'original_success_rate': original_success/total_tests*100,
        'optimized_success_rate': optimized_success/total_tests*100,
        'original_avg_confidence': np.mean(original_confidences) if original_confidences else 0,
        'optimized_avg_confidence': np.mean(optimized_confidences) if optimized_confidences else 0,
        'confidence_improvement': confidence_improvement if original_confidences and optimized_confidences else 0,
        'original_confidences': original_confidences,
        'optimized_confidences': optimized_confidences
    }

def generate_final_report(analysis_result):
    """生成最终报告"""
    print("\n" + "=" * 80)
    print("📋 最终优化报告")
    print("=" * 80)
    
    print(f"🎯 优化目标达成情况:")
    
    # 检查是否达到目标
    target_confidence = 0.7
    current_confidence = analysis_result['optimized_avg_confidence']
    
    if current_confidence >= target_confidence:
        print(f"  ✅ 平均置信度目标: {current_confidence:.3f} ≥ {target_confidence} (已达成)")
    else:
        print(f"  ⚠️ 平均置信度目标: {current_confidence:.3f} < {target_confidence} (未完全达成)")
        gap = target_confidence - current_confidence
        print(f"     距离目标还差: {gap:.3f}")
    
    success_rate = analysis_result['optimized_success_rate']
    if success_rate >= 95:
        print(f"  ✅ 识别成功率: {success_rate:.1f}% ≥ 95% (已达成)")
    else:
        print(f"  ⚠️ 识别成功率: {success_rate:.1f}% < 95% (需改进)")
    
    # 优化效果总结
    print(f"\n🚀 优化效果总结:")
    print(f"  📊 识别成功率: {analysis_result['original_success_rate']:.1f}% → {analysis_result['optimized_success_rate']:.1f}%")
    print(f"  📈 平均置信度: {analysis_result['original_avg_confidence']:.3f} → {analysis_result['optimized_avg_confidence']:.3f}")
    print(f"  🎯 置信度提升: {analysis_result['confidence_improvement']:+.3f} ({analysis_result['confidence_improvement']/analysis_result['original_avg_confidence']*100:+.1f}%)")
    
    # 技术优化总结
    print(f"\n🔧 技术优化总结:")
    print(f"  ✅ 特征工程优化: 8357维 → 1000维 (88%减少)")
    print(f"  ✅ 动态阈值调整: 固定0.6 → 自适应0.4-0.5")
    print(f"  ✅ 模型参数优化: RandomForest参数调优")
    print(f"  ✅ 样本数量提升: 3个 → 21个 (7倍增加)")
    
    # 下一步建议
    print(f"\n💡 进一步优化建议:")
    if current_confidence < target_confidence:
        print(f"  🔸 继续优化特征工程，尝试更高级的特征选择方法")
        print(f"  🔸 考虑使用深度学习方法进行特征提取")
        print(f"  🔸 增加更多高质量的训练样本")
        print(f"  🔸 实施数据增强技术")
    else:
        print(f"  🎉 当前性能已达到预期目标！")
        print(f"  🔸 可以考虑部署到生产环境")
        print(f"  🔸 建立持续监控和优化机制")
    
    # 保存最终报告
    final_report = {
        'timestamp': datetime.now().isoformat(),
        'optimization_summary': {
            'target_confidence': target_confidence,
            'achieved_confidence': current_confidence,
            'target_achieved': current_confidence >= target_confidence,
            'success_rate': success_rate,
            'confidence_improvement': analysis_result['confidence_improvement']
        },
        'technical_optimizations': {
            'feature_reduction': '8357 → 1000 dimensions',
            'dynamic_thresholds': 'Implemented adaptive thresholds',
            'model_optimization': 'RandomForest parameter tuning',
            'sample_increase': '3 → 21 samples (7x)'
        },
        'detailed_results': analysis_result
    }
    
    with open('final_optimization_report.json', 'w', encoding='utf-8') as f:
        json.dump(final_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 最终报告已保存到: final_optimization_report.json")

def main():
    """主函数"""
    print("🎯 最终优化系统测试")
    print("验证特征工程、动态阈值和模型优化的综合效果...")
    
    try:
        # 1. 最终对比测试
        original_results, optimized_results = final_comparison_test()
        
        # 2. 分析结果
        analysis_result = analyze_final_results(original_results, optimized_results)
        
        # 3. 生成最终报告
        generate_final_report(analysis_result)
        
        print("\n" + "=" * 80)
        print("✅ 最终优化测试完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
