#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值调整脚本
优化置信度阈值以平衡准确率和召回率
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime

sys.path.append('.')
from optimized_workpiece_learner import OptimizedWorkpieceLearner

def analyze_confidence_distribution():
    """分析置信度分布"""
    print("🔍 分析置信度分布")
    print("=" * 60)
    
    learner = OptimizedWorkpieceLearner()
    
    # 确保模型已训练
    if not learner.is_trained:
        print("训练优化模型...")
        success = learner.train_optimized_model()
        if not success:
            print("❌ 模型训练失败")
            return None
    
    # 获取所有测试图像
    images_dir = "./workpiece_data/images"
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    
    print(f"找到 {len(image_files)} 个测试图像")
    
    # 收集所有识别结果
    all_results = []
    confidence_by_class = {}
    
    for image_path in image_files:
        try:
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            # 使用较低的阈值进行识别，收集所有结果
            original_thresholds = learner.dynamic_thresholds.copy()
            learner.dynamic_thresholds = {k: 0.1 for k in original_thresholds.keys()}  # 临时设置很低的阈值
            learner.default_threshold = 0.1
            
            result = learner.recognize_workpiece_optimized(image)
            
            # 恢复原始阈值
            learner.dynamic_thresholds = original_thresholds
            learner.default_threshold = 0.45
            
            if result['success']:
                workpiece_name = result['workpiece_name']
                confidence = result['confidence']
                
                all_results.append({
                    'image': os.path.basename(image_path),
                    'workpiece': workpiece_name,
                    'confidence': confidence
                })
                
                if workpiece_name not in confidence_by_class:
                    confidence_by_class[workpiece_name] = []
                confidence_by_class[workpiece_name].append(confidence)
                
        except Exception as e:
            print(f"处理图像 {os.path.basename(image_path)} 时出错: {e}")
    
    print(f"\n📊 收集到 {len(all_results)} 个有效识别结果")
    
    # 分析每个类别的置信度分布
    print(f"\n📈 各类别置信度分布:")
    optimal_thresholds = {}
    
    for workpiece_name, confidences in confidence_by_class.items():
        confidences = np.array(confidences)
        
        print(f"\n🎯 {workpiece_name}:")
        print(f"  样本数: {len(confidences)}")
        print(f"  平均置信度: {np.mean(confidences):.3f}")
        print(f"  最高置信度: {np.max(confidences):.3f}")
        print(f"  最低置信度: {np.min(confidences):.3f}")
        print(f"  标准差: {np.std(confidences):.3f}")
        
        # 计算不同分位数
        percentiles = [10, 25, 50, 75, 90]
        print(f"  分位数: ", end="")
        for p in percentiles:
            print(f"{p}%={np.percentile(confidences, p):.3f} ", end="")
        print()
        
        # 推荐阈值：使用15%分位数，确保85%的样本能通过
        recommended_threshold = np.percentile(confidences, 15)
        # 限制阈值范围在0.3-0.6之间
        optimal_thresholds[workpiece_name] = max(min(recommended_threshold, 0.6), 0.3)
        print(f"  推荐阈值: {optimal_thresholds[workpiece_name]:.3f}")
    
    return learner, all_results, optimal_thresholds

def test_threshold_performance(learner, optimal_thresholds):
    """测试不同阈值的性能"""
    print(f"\n🧪 测试阈值性能")
    print("=" * 60)
    
    # 获取测试图像
    images_dir = "./workpiece_data/images"
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    
    # 测试不同阈值策略
    threshold_strategies = {
        "原始阈值": learner.dynamic_thresholds.copy(),
        "优化阈值": optimal_thresholds,
        "固定低阈值": {k: 0.4 for k in optimal_thresholds.keys()},
        "固定中阈值": {k: 0.5 for k in optimal_thresholds.keys()}
    }
    
    results_by_strategy = {}
    
    for strategy_name, thresholds in threshold_strategies.items():
        print(f"\n📊 测试策略: {strategy_name}")
        print(f"  阈值设置: {thresholds}")
        
        # 设置阈值
        learner.dynamic_thresholds = thresholds
        learner.default_threshold = 0.45
        
        success_count = 0
        total_count = 0
        confidences = []
        
        for image_path in image_files[:15]:  # 测试前15个图像
            try:
                image = cv2.imread(image_path)
                if image is None:
                    continue
                
                result = learner.recognize_workpiece_optimized(image)
                total_count += 1
                
                if result['success']:
                    success_count += 1
                    confidences.append(result['confidence'])
                    
            except Exception as e:
                total_count += 1
        
        success_rate = success_count / total_count * 100 if total_count > 0 else 0
        avg_confidence = np.mean(confidences) if confidences else 0
        
        results_by_strategy[strategy_name] = {
            'success_rate': success_rate,
            'success_count': success_count,
            'total_count': total_count,
            'avg_confidence': avg_confidence,
            'confidences': confidences
        }
        
        print(f"  成功率: {success_rate:.1f}% ({success_count}/{total_count})")
        print(f"  平均置信度: {avg_confidence:.3f}")
    
    return results_by_strategy

def apply_optimal_thresholds(learner, optimal_thresholds):
    """应用最优阈值"""
    print(f"\n🎯 应用最优阈值")
    print("=" * 60)
    
    print(f"原始阈值: {learner.dynamic_thresholds}")
    print(f"优化阈值: {optimal_thresholds}")
    
    # 更新阈值
    learner.dynamic_thresholds = optimal_thresholds
    learner.save_threshold_config()
    
    print("✅ 最优阈值已应用并保存")

def final_performance_test(learner):
    """最终性能测试"""
    print(f"\n🏆 最终性能测试")
    print("=" * 60)
    
    # 获取测试图像
    images_dir = "./workpiece_data/images"
    image_files = glob.glob(os.path.join(images_dir, "*.jpg"))
    
    print(f"测试 {len(image_files)} 个图像...")
    
    success_count = 0
    confidences = []
    results_by_class = {}
    
    for image_path in image_files:
        try:
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            result = learner.recognize_workpiece_optimized(image)
            
            if result['success']:
                success_count += 1
                confidence = result['confidence']
                confidences.append(confidence)
                workpiece_name = result['workpiece_name']
                
                if workpiece_name not in results_by_class:
                    results_by_class[workpiece_name] = []
                results_by_class[workpiece_name].append(confidence)
                
        except Exception as e:
            print(f"测试图像 {os.path.basename(image_path)} 时出错: {e}")
    
    total_count = len(image_files)
    success_rate = success_count / total_count * 100
    avg_confidence = np.mean(confidences) if confidences else 0
    
    print(f"\n📊 最终性能结果:")
    print(f"  总测试图像: {total_count}")
    print(f"  成功识别: {success_count}")
    print(f"  识别成功率: {success_rate:.1f}%")
    print(f"  平均置信度: {avg_confidence:.3f}")
    
    if confidences:
        print(f"  最高置信度: {np.max(confidences):.3f}")
        print(f"  最低置信度: {np.min(confidences):.3f}")
        print(f"  置信度标准差: {np.std(confidences):.3f}")
        
        # 置信度分布
        excellent = len([c for c in confidences if c >= 0.8])
        good = len([c for c in confidences if 0.6 <= c < 0.8])
        fair = len([c for c in confidences if 0.4 <= c < 0.6])
        poor = len([c for c in confidences if c < 0.4])
        
        print(f"\n📊 置信度分布:")
        print(f"  优秀 (≥0.8): {excellent} 个 ({excellent/len(confidences)*100:.1f}%)")
        print(f"  良好 (0.6-0.8): {good} 个 ({good/len(confidences)*100:.1f}%)")
        print(f"  一般 (0.4-0.6): {fair} 个 ({fair/len(confidences)*100:.1f}%)")
        print(f"  较差 (<0.4): {poor} 个 ({poor/len(confidences)*100:.1f}%)")
    
    # 各类别性能
    print(f"\n🎯 各类别性能:")
    for workpiece_name, class_confidences in results_by_class.items():
        print(f"  {workpiece_name}: {len(class_confidences)} 次识别, 平均置信度 {np.mean(class_confidences):.3f}")
    
    return {
        'success_rate': success_rate,
        'avg_confidence': avg_confidence,
        'confidences': confidences,
        'results_by_class': results_by_class
    }

def main():
    """主函数"""
    print("🎯 阈值优化和调整")
    print("目标：优化置信度阈值以提升识别性能...")
    
    try:
        # 1. 分析置信度分布
        learner, all_results, optimal_thresholds = analyze_confidence_distribution()
        if learner is None:
            return
        
        # 2. 测试不同阈值策略
        strategy_results = test_threshold_performance(learner, optimal_thresholds)
        
        # 3. 选择最佳策略
        best_strategy = None
        best_score = 0
        
        for strategy_name, result in strategy_results.items():
            # 综合评分：成功率权重0.7，平均置信度权重0.3
            score = result['success_rate'] * 0.7 + result['avg_confidence'] * 100 * 0.3
            print(f"{strategy_name} 综合评分: {score:.1f}")
            
            if score > best_score:
                best_score = score
                best_strategy = strategy_name
        
        print(f"\n🏆 最佳策略: {best_strategy}")
        
        # 4. 应用最优阈值
        if best_strategy == "优化阈值":
            apply_optimal_thresholds(learner, optimal_thresholds)
        elif best_strategy == "固定低阈值":
            apply_optimal_thresholds(learner, {k: 0.4 for k in optimal_thresholds.keys()})
        elif best_strategy == "固定中阈值":
            apply_optimal_thresholds(learner, {k: 0.5 for k in optimal_thresholds.keys()})
        
        # 5. 最终性能测试
        final_result = final_performance_test(learner)
        
        # 6. 保存结果
        optimization_result = {
            'timestamp': datetime.now().isoformat(),
            'optimal_thresholds': optimal_thresholds,
            'best_strategy': best_strategy,
            'strategy_results': strategy_results,
            'final_performance': final_result
        }
        
        with open('threshold_optimization_result.json', 'w', encoding='utf-8') as f:
            json.dump(optimization_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 阈值优化结果已保存到: threshold_optimization_result.json")
        print("\n✅ 阈值优化完成！")
        
    except Exception as e:
        print(f"❌ 阈值优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
