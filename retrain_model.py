#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新训练模型脚本
"""

import sys
sys.path.append('.')

from custom_workpiece_learner import CustomWorkpieceLearner

def main():
    print('🔄 检查模型训练状态...')
    learner = CustomWorkpieceLearner()

    # 检查是否需要重新训练
    model_info = learner.get_model_info()
    print(f'模型已训练: {model_info["is_trained"]}')
    print(f'模型文件存在: {model_info["model_exists"]}')

    if not model_info['is_trained']:
        print('\n🚀 开始重新训练模型...')
        success = learner.train_model()
        if success:
            print('✅ 模型训练成功！')
            
            # 重新检查模型状态
            updated_info = learner.get_model_info()
            print(f'更新后模型状态: {updated_info["is_trained"]}')
        else:
            print('❌ 模型训练失败')
    else:
        print('✅ 模型已是最新状态')

if __name__ == "__main__":
    main()
