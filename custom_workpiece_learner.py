#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义工件学习识别系统
支持用户自定义工件的学习、训练和识别

主要功能:
1. 工件图像学习和标注
2. 特征提取和模型训练  
3. 实时工件识别和信息返回
4. 工件数据库管理
"""

import cv2
import numpy as np
import json
import os
import pickle
import time
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib

class CustomWorkpieceLearner:
    """自定义工件学习识别器"""
    
    def __init__(self, data_dir: str = "./workpiece_data"):
        """
        初始化学习器
        
        Args:
            data_dir: 工件数据存储目录
        """
        self.data_dir = data_dir
        self.models_dir = os.path.join(data_dir, "models")
        self.images_dir = os.path.join(data_dir, "images")
        self.annotations_dir = os.path.join(data_dir, "annotations")
        
        # 创建必要的目录
        for dir_path in [self.data_dir, self.models_dir, self.images_dir, self.annotations_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 设置日志器（必须在其他操作之前）
        self.logger = self._setup_logger()

        # 工件数据库
        self.workpiece_database = {}
        self.load_workpiece_database()

        # 机器学习模型
        self.feature_extractor = None
        self.classifier = None
        self.scaler = StandardScaler()
        self.is_trained = False

        # 特征提取器类型
        self.feature_type = "combined"  # "sift", "orb", "lbp", "combined"
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("CustomWorkpieceLearner")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def learn_workpiece(self, image: np.ndarray, bbox: Tuple[int, int, int, int], 
                       workpiece_info: Dict) -> str:
        """
        学习新工件
        
        Args:
            image: 原始图像
            bbox: 工件边界框 (x, y, width, height)
            workpiece_info: 工件信息字典，包含name, type, specifications等
            
        Returns:
            工件ID
        """
        try:
            # 生成唯一工件ID
            workpiece_id = f"workpiece_{int(time.time() * 1000)}"
            
            # 提取工件区域
            x, y, w, h = bbox
            workpiece_roi = image[y:y+h, x:x+w]
            
            # 保存工件图像
            image_filename = f"{workpiece_id}.jpg"
            image_path = os.path.join(self.images_dir, image_filename)
            cv2.imwrite(image_path, workpiece_roi)
            
            # 提取特征
            features = self.extract_features(workpiece_roi)
            
            # 创建工件记录
            workpiece_record = {
                'id': workpiece_id,
                'info': workpiece_info,
                'image_path': image_path,
                'bbox': bbox,
                'features': features.tolist() if isinstance(features, np.ndarray) else features,
                'created_time': datetime.now().isoformat(),
                'learn_count': 1
            }
            
            # 添加到数据库
            self.workpiece_database[workpiece_id] = workpiece_record
            
            # 保存数据库
            self.save_workpiece_database()
            
            self.logger.info(f"学习新工件: {workpiece_info.get('name', 'Unknown')} (ID: {workpiece_id})")
            return workpiece_id
            
        except Exception as e:
            self.logger.error(f"学习工件失败: {e}")
            return None
    
    def add_workpiece_sample(self, workpiece_id: str, image: np.ndarray, 
                           bbox: Tuple[int, int, int, int]) -> bool:
        """
        为已有工件添加新样本
        
        Args:
            workpiece_id: 工件ID
            image: 原始图像
            bbox: 工件边界框
            
        Returns:
            是否成功
        """
        try:
            if workpiece_id not in self.workpiece_database:
                self.logger.error(f"工件ID不存在: {workpiece_id}")
                return False
            
            # 提取工件区域
            x, y, w, h = bbox
            workpiece_roi = image[y:y+h, x:x+w]
            
            # 保存额外样本图像
            sample_count = self.workpiece_database[workpiece_id]['learn_count']
            image_filename = f"{workpiece_id}_sample_{sample_count}.jpg"
            image_path = os.path.join(self.images_dir, image_filename)
            cv2.imwrite(image_path, workpiece_roi)
            
            # 提取特征
            features = self.extract_features(workpiece_roi)
            
            # 更新工件记录
            record = self.workpiece_database[workpiece_id]
            if 'additional_samples' not in record:
                record['additional_samples'] = []
            
            record['additional_samples'].append({
                'image_path': image_path,
                'bbox': bbox,
                'features': features.tolist() if isinstance(features, np.ndarray) else features,
                'added_time': datetime.now().isoformat()
            })
            
            record['learn_count'] += 1
            
            # 保存数据库
            self.save_workpiece_database()
            
            self.logger.info(f"为工件 {workpiece_id} 添加新样本")
            return True
            
        except Exception as e:
            self.logger.error(f"添加工件样本失败: {e}")
            return False
    
    def extract_features(self, image: np.ndarray) -> np.ndarray:
        """
        提取图像特征
        
        Args:
            image: 输入图像
            
        Returns:
            特征向量
        """
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            features = []
            
            if self.feature_type in ["sift", "combined"]:
                # SIFT特征
                sift_features = self._extract_sift_features(gray)
                features.extend(sift_features)
            
            if self.feature_type in ["orb", "combined"]:
                # ORB特征
                orb_features = self._extract_orb_features(gray)
                features.extend(orb_features)
            
            if self.feature_type in ["lbp", "combined"]:
                # LBP纹理特征
                lbp_features = self._extract_lbp_features(gray)
                features.extend(lbp_features)
            
            # 颜色直方图特征
            color_features = self._extract_color_features(image)
            features.extend(color_features)

            # 增强颜色特征 (特别针对绿色工件)
            enhanced_color_features = self._extract_enhanced_color_features(image)
            features.extend(enhanced_color_features)

            # 形状特征
            shape_features = self._extract_shape_features(gray)
            features.extend(shape_features)

            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return np.array([])
    
    def _extract_sift_features(self, gray: np.ndarray, max_features: int = 50) -> List[float]:
        """提取SIFT特征"""
        try:
            sift = cv2.SIFT_create()
            keypoints, descriptors = sift.detectAndCompute(gray, None)
            
            if descriptors is not None:
                # 使用前max_features个特征点
                descriptors = descriptors[:max_features]
                # 展平并填充到固定长度
                features = descriptors.flatten()
                target_length = max_features * 128  # SIFT描述子是128维
                if len(features) < target_length:
                    features = np.pad(features, (0, target_length - len(features)))
                else:
                    features = features[:target_length]
                return features.tolist()
            else:
                return [0.0] * (max_features * 128)
        except:
            return [0.0] * (max_features * 128)
    
    def _extract_orb_features(self, gray: np.ndarray, max_features: int = 50) -> List[float]:
        """提取ORB特征"""
        try:
            orb = cv2.ORB_create(nfeatures=max_features)
            keypoints, descriptors = orb.detectAndCompute(gray, None)
            
            if descriptors is not None:
                # ORB描述子是32维的二进制描述子
                features = descriptors.flatten().astype(np.float32)
                target_length = max_features * 32
                if len(features) < target_length:
                    features = np.pad(features, (0, target_length - len(features)))
                else:
                    features = features[:target_length]
                return features.tolist()
            else:
                return [0.0] * (max_features * 32)
        except:
            return [0.0] * (max_features * 32)
    
    def _extract_lbp_features(self, gray: np.ndarray) -> List[float]:
        """提取LBP纹理特征"""
        try:
            # 简化的LBP实现
            lbp = np.zeros_like(gray)
            for i in range(1, gray.shape[0]-1):
                for j in range(1, gray.shape[1]-1):
                    center = gray[i, j]
                    code = 0
                    code |= (gray[i-1, j-1] >= center) << 7
                    code |= (gray[i-1, j] >= center) << 6
                    code |= (gray[i-1, j+1] >= center) << 5
                    code |= (gray[i, j+1] >= center) << 4
                    code |= (gray[i+1, j+1] >= center) << 3
                    code |= (gray[i+1, j] >= center) << 2
                    code |= (gray[i+1, j-1] >= center) << 1
                    code |= (gray[i, j-1] >= center) << 0
                    lbp[i, j] = code
            
            # 计算LBP直方图
            hist, _ = np.histogram(lbp.ravel(), bins=256, range=(0, 256))
            hist = hist.astype(np.float32)
            hist /= (hist.sum() + 1e-7)  # 归一化
            return hist.tolist()
        except:
            return [0.0] * 256
    
    def _extract_color_features(self, image: np.ndarray) -> List[float]:
        """提取颜色特征"""
        try:
            if len(image.shape) == 3:
                # BGR颜色直方图
                features = []
                for i in range(3):
                    hist = cv2.calcHist([image], [i], None, [32], [0, 256])
                    hist = hist.flatten()
                    hist = hist / (hist.sum() + 1e-7)  # 归一化
                    features.extend(hist.tolist())
                return features
            else:
                # 灰度直方图
                hist = cv2.calcHist([image], [0], None, [32], [0, 256])
                hist = hist.flatten()
                hist = hist / (hist.sum() + 1e-7)
                return hist.tolist()
        except:
            return [0.0] * 96  # 3*32 for BGR or 32 for gray

    def _extract_enhanced_color_features(self, image: np.ndarray) -> List[float]:
        """
        提取增强颜色特征，特别针对绿色工件识别
        """
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # 定义颜色范围 (HSV)
            color_ranges = {
                'green': ([40, 50, 50], [80, 255, 255]),    # 绿色范围
                'blue': ([100, 50, 50], [130, 255, 255]),   # 蓝色范围
                'red1': ([0, 50, 50], [10, 255, 255]),      # 红色范围1
                'red2': ([170, 50, 50], [180, 255, 255]),   # 红色范围2
                'yellow': ([20, 50, 50], [40, 255, 255])    # 黄色范围
            }

            color_features = []

            # 为每种颜色计算特征
            for color_name, (lower, upper) in color_ranges.items():
                lower_bound = np.array(lower)
                upper_bound = np.array(upper)

                # 创建颜色掩码
                mask = cv2.inRange(hsv, lower_bound, upper_bound)

                # 计算颜色占比
                color_ratio = np.sum(mask > 0) / (image.shape[0] * image.shape[1])

                # 计算颜色区域的统计特征
                if np.sum(mask > 0) > 0:
                    color_mean_h = np.mean(hsv[mask > 0, 0])  # 色调均值
                    color_mean_s = np.mean(hsv[mask > 0, 1])  # 饱和度均值
                    color_mean_v = np.mean(hsv[mask > 0, 2])  # 明度均值
                    color_std_s = np.std(hsv[mask > 0, 1])    # 饱和度标准差
                    color_std_v = np.std(hsv[mask > 0, 2])    # 明度标准差
                else:
                    color_mean_h = 0
                    color_mean_s = 0
                    color_mean_v = 0
                    color_std_s = 0
                    color_std_v = 0

                color_features.extend([color_ratio, color_mean_h, color_mean_s, color_mean_v, color_std_s, color_std_v])

            # 添加整体HSV统计特征
            h_mean = np.mean(hsv[:, :, 0])
            s_mean = np.mean(hsv[:, :, 1])
            v_mean = np.mean(hsv[:, :, 2])
            h_std = np.std(hsv[:, :, 0])
            s_std = np.std(hsv[:, :, 1])
            v_std = np.std(hsv[:, :, 2])

            color_features.extend([h_mean, s_mean, v_mean, h_std, s_std, v_std])

            # 添加绿色特殊特征 (针对绿色小铁片)
            green_mask = cv2.inRange(hsv, np.array([40, 50, 50]), np.array([80, 255, 255]))
            green_ratio = np.sum(green_mask > 0) / (image.shape[0] * image.shape[1])

            # 绿色区域的形状特征
            if np.sum(green_mask > 0) > 100:  # 如果绿色区域足够大
                contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if contours:
                    largest_green_contour = max(contours, key=cv2.contourArea)
                    green_area = cv2.contourArea(largest_green_contour)
                    green_perimeter = cv2.arcLength(largest_green_contour, True)
                    green_circularity = 4 * np.pi * green_area / (green_perimeter * green_perimeter) if green_perimeter > 0 else 0
                else:
                    green_area = 0
                    green_perimeter = 0
                    green_circularity = 0
            else:
                green_area = 0
                green_perimeter = 0
                green_circularity = 0

            color_features.extend([green_ratio * 10, green_area / 1000, green_perimeter / 100, green_circularity])  # 放大绿色特征权重

            return color_features

        except Exception as e:
            self.logger.warning(f"增强颜色特征提取失败: {e}")
            return [0.0] * 40  # 5种颜色*6特征 + 6个整体特征 + 4个绿色特征
    
    def _extract_shape_features(self, gray: np.ndarray) -> List[float]:
        """提取形状特征"""
        try:
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 找到最大轮廓
                largest_contour = max(contours, key=cv2.contourArea)
                
                # 计算形状特征
                area = cv2.contourArea(largest_contour)
                perimeter = cv2.arcLength(largest_contour, True)
                
                # 边界矩形
                x, y, w, h = cv2.boundingRect(largest_contour)
                aspect_ratio = w / h if h > 0 else 0
                
                # 轮廓面积与边界矩形面积的比值
                rect_area = w * h
                extent = area / rect_area if rect_area > 0 else 0
                
                # 圆形度
                circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
                
                return [area, perimeter, aspect_ratio, extent, circularity]
            else:
                return [0.0, 0.0, 1.0, 0.0, 0.0]
        except:
            return [0.0, 0.0, 1.0, 0.0, 0.0]

    def train_model(self) -> bool:
        """
        训练工件识别模型

        Returns:
            是否训练成功
        """
        try:
            if len(self.workpiece_database) < 2:
                self.logger.warning("需要至少2种工件才能训练模型")
                return False

            # 准备训练数据
            X, y, labels = self._prepare_training_data()

            if len(X) == 0:
                self.logger.error("没有有效的训练数据")
                return False

            # 数据标准化
            X_scaled = self.scaler.fit_transform(X)

            # 分割训练和测试数据
            unique_classes = len(set(y))
            min_samples_per_class = min([y.count(cls) for cls in set(y)])

            if len(X) > unique_classes * 2 and min_samples_per_class >= 2:
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y, test_size=0.2, random_state=42, stratify=y
                )
            else:
                # 样本太少，使用全部数据进行训练和测试
                X_train, X_test, y_train, y_test = X_scaled, X_scaled, y, y

            # 训练分类器
            self.classifier = RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                max_depth=10,
                min_samples_split=2
            )

            self.classifier.fit(X_train, y_train)

            # 评估模型
            if len(X_test) > 0:
                y_pred = self.classifier.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                self.logger.info(f"模型训练完成，准确率: {accuracy:.3f}")

                # 打印详细报告
                if len(set(y_test)) > 1:
                    report = classification_report(y_test, y_pred, target_names=labels, zero_division=0)
                    self.logger.info(f"分类报告:\n{report}")

            # 保存模型
            self._save_model()

            self.is_trained = True
            self.logger.info("模型训练并保存成功")
            return True

        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            return False

    def _prepare_training_data(self) -> Tuple[List, List, List]:
        """准备训练数据"""
        X, y, labels = [], [], []
        label_map = {}
        current_label = 0

        for workpiece_id, record in self.workpiece_database.items():
            workpiece_name = record['info'].get('name', workpiece_id)

            # 为每个工件类型分配标签
            if workpiece_name not in label_map:
                label_map[workpiece_name] = current_label
                labels.append(workpiece_name)
                current_label += 1

            label = label_map[workpiece_name]

            # 添加主样本
            if 'features' in record and record['features']:
                X.append(record['features'])
                y.append(label)

            # 添加额外样本
            if 'additional_samples' in record:
                for sample in record['additional_samples']:
                    if 'features' in sample and sample['features']:
                        X.append(sample['features'])
                        y.append(label)

        return X, y, labels

    def _save_model(self):
        """保存训练好的模型"""
        try:
            model_data = {
                'classifier': self.classifier,
                'scaler': self.scaler,
                'feature_type': self.feature_type,
                'trained_time': datetime.now().isoformat(),
                'workpiece_count': len(self.workpiece_database)
            }

            model_path = os.path.join(self.models_dir, "workpiece_classifier.pkl")
            joblib.dump(model_data, model_path)

            self.logger.info(f"模型已保存到: {model_path}")

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")

    def load_model(self) -> bool:
        """加载训练好的模型"""
        try:
            model_path = os.path.join(self.models_dir, "workpiece_classifier.pkl")

            if not os.path.exists(model_path):
                self.logger.warning("模型文件不存在")
                return False

            model_data = joblib.load(model_path)

            self.classifier = model_data['classifier']
            self.scaler = model_data['scaler']
            self.feature_type = model_data.get('feature_type', 'combined')
            self.is_trained = True

            self.logger.info("模型加载成功")
            return True

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False

    def recognize_workpiece(self, image: np.ndarray, bbox: Tuple[int, int, int, int] = None) -> Dict:
        """
        识别工件

        Args:
            image: 输入图像
            bbox: 可选的工件边界框，如果不提供则自动检测

        Returns:
            识别结果字典
        """
        try:
            if not self.is_trained:
                if not self.load_model():
                    return {'success': False, 'error': '模型未训练或加载失败'}

            # 如果没有提供边界框，尝试自动检测
            if bbox is None:
                detected_objects = self._detect_objects(image)
                if not detected_objects:
                    return {'success': False, 'error': '未检测到工件'}
                bbox = detected_objects[0]  # 使用第一个检测到的对象

            # 提取工件区域
            x, y, w, h = bbox
            workpiece_roi = image[y:y+h, x:x+w]

            # 提取特征
            features = self.extract_features(workpiece_roi)

            if len(features) == 0:
                return {'success': False, 'error': '特征提取失败'}

            # 预测
            features_scaled = self.scaler.transform([features])
            prediction = self.classifier.predict(features_scaled)[0]
            probabilities = self.classifier.predict_proba(features_scaled)[0]
            confidence = np.max(probabilities)

            # 获取预测的工件名称
            _, _, labels = self._prepare_training_data()
            predicted_workpiece = labels[prediction] if prediction < len(labels) else "Unknown"

            # 查找对应的工件信息
            workpiece_info = None
            for record in self.workpiece_database.values():
                if record['info'].get('name') == predicted_workpiece:
                    workpiece_info = record['info'].copy()
                    break

            result = {
                'success': True,
                'workpiece_name': predicted_workpiece,
                'confidence': float(confidence),
                'bbox': bbox,
                'center_x': x + w // 2,
                'center_y': y + h // 2,
                'workpiece_info': workpiece_info,
                'timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"识别结果: {predicted_workpiece} (置信度: {confidence:.3f})")
            return result

        except Exception as e:
            self.logger.error(f"工件识别失败: {e}")
            return {'success': False, 'error': str(e)}

    def enhanced_recognize_workpiece(self, image: np.ndarray, bbox: Optional[Tuple[int, int, int, int]] = None) -> Dict:
        """
        增强识别方法 - 大幅提升识别准确率
        使用多种图像预处理和多尺度检测技术

        Args:
            image: 输入图像
            bbox: 可选的工件边界框

        Returns:
            增强识别结果字典
        """
        try:
            self.logger.info("开始增强识别...")
            all_results = []

            # 1. 原始识别
            original_result = self.recognize_workpiece(image, bbox)
            if original_result['success']:
                all_results.append(('original', original_result))
                self.logger.info(f"原始识别: {original_result['workpiece_name']} ({original_result['confidence']:.3f})")

            # 2. 图像预处理增强
            enhanced_images = self._create_enhanced_images(image)

            for method_name, enhanced_img in enhanced_images:
                try:
                    result = self.recognize_workpiece(enhanced_img, bbox)
                    if result['success']:
                        all_results.append((method_name, result))
                        self.logger.info(f"{method_name}识别: {result['workpiece_name']} ({result['confidence']:.3f})")
                except Exception as e:
                    self.logger.warning(f"{method_name}识别失败: {e}")

            # 3. 多尺度检测
            scale_results = self._multi_scale_detection(image, bbox)
            all_results.extend(scale_results)

            # 4. 结果融合
            if all_results:
                final_result = self._fuse_recognition_results(all_results)
                final_result['enhanced'] = True
                self.logger.info(f"增强识别完成: {final_result.get('workpiece_name', 'Unknown')} (置信度: {final_result.get('confidence', 0):.3f}, 融合数: {final_result.get('fusion_count', 0)})")
                return final_result
            else:
                return {'success': False, 'error': '所有增强方法都未能识别出工件'}

        except Exception as e:
            self.logger.error(f"增强识别失败: {e}")
            return {'success': False, 'error': str(e)}

    def _create_enhanced_images(self, image: np.ndarray) -> List[Tuple[str, np.ndarray]]:
        """创建增强图像"""
        enhanced_images = []

        try:
            # 1. 直方图均衡化
            if len(image.shape) == 3:
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:,:,0] = cv2.equalizeHist(lab[:,:,0])
                hist_eq = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                hist_eq = cv2.equalizeHist(image)
            enhanced_images.append(('直方图均衡化', hist_eq))

            # 2. 高斯模糊去噪
            gaussian = cv2.GaussianBlur(image, (3, 3), 0)
            enhanced_images.append(('高斯模糊', gaussian))

            # 3. 双边滤波
            bilateral = cv2.bilateralFilter(image, 9, 75, 75)
            enhanced_images.append(('双边滤波', bilateral))

            # 4. 形态学操作
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            kernel = np.ones((3,3), np.uint8)
            morphed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            if len(image.shape) == 3:
                morphed = cv2.cvtColor(morphed, cv2.COLOR_GRAY2BGR)
            enhanced_images.append(('形态学操作', morphed))

            # 5. 对比度增强
            if len(image.shape) == 3:
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:,:,0] = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(lab[:,:,0])
                clahe = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(image)
            enhanced_images.append(('对比度增强', clahe))

        except Exception as e:
            self.logger.warning(f"创建增强图像失败: {e}")

        return enhanced_images

    def _multi_scale_detection(self, image: np.ndarray, bbox: Optional[Tuple[int, int, int, int]] = None) -> List[Tuple[str, Dict]]:
        """多尺度检测"""
        scale_results = []
        scales = [0.8, 1.2, 1.5]  # 不同的缩放比例

        for scale in scales:
            try:
                # 缩放图像
                height, width = image.shape[:2]
                new_height, new_width = int(height * scale), int(width * scale)
                scaled_image = cv2.resize(image, (new_width, new_height))

                # 调整bbox
                scaled_bbox = None
                if bbox is not None:
                    x, y, w, h = bbox
                    scaled_bbox = (int(x * scale), int(y * scale), int(w * scale), int(h * scale))

                # 识别
                result = self.recognize_workpiece(scaled_image, scaled_bbox)
                if result['success']:
                    # 调整坐标回原始尺度
                    if 'center_x' in result:
                        result['center_x'] = int(result['center_x'] / scale)
                    if 'center_y' in result:
                        result['center_y'] = int(result['center_y'] / scale)
                    if 'bbox' in result:
                        bx, by, bw, bh = result['bbox']
                        result['bbox'] = (int(bx / scale), int(by / scale), int(bw / scale), int(bh / scale))

                    result['scale_factor'] = scale
                    scale_results.append((f'多尺度_{scale}', result))
                    self.logger.info(f"尺度{scale}识别: {result['workpiece_name']} ({result['confidence']:.3f})")

            except Exception as e:
                self.logger.warning(f"尺度{scale}检测失败: {e}")

        return scale_results

    def _fuse_recognition_results(self, results: List[Tuple[str, Dict]]) -> Dict:
        """融合多个识别结果"""
        if not results:
            return {'success': False, 'error': '没有结果可融合'}

        # 按工件名称分组
        workpiece_groups = {}
        for method, result in results:
            name = result['workpiece_name']
            if name not in workpiece_groups:
                workpiece_groups[name] = []
            workpiece_groups[name].append((method, result))

        # 计算每个工件的加权分数
        best_name = None
        best_score = 0
        best_group = None

        for name, group in workpiece_groups.items():
            # 计算加权分数
            total_confidence = 0
            method_weights = {
                'original': 1.0,
                '直方图均衡化': 0.8,
                '高斯模糊': 0.7,
                '双边滤波': 0.8,
                '形态学操作': 0.6,
                '对比度增强': 0.8,
                '多尺度_0.8': 0.9,
                '多尺度_1.2': 0.9,
                '多尺度_1.5': 0.8
            }

            weighted_confidence = 0
            total_weight = 0

            for method, result in group:
                weight = method_weights.get(method, 0.5)
                weighted_confidence += result['confidence'] * weight
                total_weight += weight

            if total_weight > 0:
                avg_confidence = weighted_confidence / total_weight
            else:
                avg_confidence = 0

            # 多方法确认奖励
            count_bonus = len(group) * 0.05
            score = avg_confidence + count_bonus

            if score > best_score:
                best_score = score
                best_name = name
                best_group = group

        if best_name and best_group:
            # 选择该组中置信度最高的结果作为基础
            best_method, best_result = max(best_group, key=lambda x: x[1]['confidence'])

            # 更新结果
            final_result = best_result.copy()
            final_result['fusion_count'] = len(best_group)
            final_result['fusion_score'] = best_score
            final_result['best_method'] = best_method
            final_result['all_methods'] = [method for method, _ in best_group]

            return final_result

        return {'success': False, 'error': '融合失败'}

    def ensemble_recognize_workpiece(self, image: np.ndarray, bbox: Optional[Tuple[int, int, int, int]] = None) -> Dict:
        """
        集成识别方法 - 进一步大幅提升识别准确率
        使用多种不同的预处理方法和集成策略

        Args:
            image: 输入图像
            bbox: 可选的工件边界框

        Returns:
            集成识别结果字典
        """
        try:
            self.logger.info("开始集成识别...")
            all_results = []

            # 1. 增强识别 (已有的最佳方法)
            enhanced_result = self.enhanced_recognize_workpiece(image, bbox)
            if enhanced_result['success']:
                enhanced_result['method'] = 'enhanced'
                enhanced_result['weight'] = 2.0  # 最高权重
                all_results.append(enhanced_result)
                self.logger.info(f"增强识别: {enhanced_result['workpiece_name']} ({enhanced_result['confidence']:.3f})")

            # 2. 不同预处理方法的识别
            preprocessing_methods = self._get_additional_preprocessing_methods(image)

            for method_name, processed_image in preprocessing_methods:
                try:
                    result = self.recognize_workpiece(processed_image, bbox)
                    if result['success']:
                        result['method'] = method_name
                        result['weight'] = self._get_method_weight(method_name)
                        all_results.append(result)
                        self.logger.info(f"{method_name}识别: {result['workpiece_name']} ({result['confidence']:.3f})")
                except Exception as e:
                    self.logger.warning(f"{method_name}识别失败: {e}")

            # 3. 多角度旋转识别
            rotation_results = self._rotation_invariant_recognition(image, bbox)
            all_results.extend(rotation_results)

            # 4. 高级集成融合
            if all_results:
                final_result = self._advanced_ensemble_fusion(all_results)
                final_result['ensemble_advanced'] = True
                self.logger.info(f"集成识别完成: {final_result.get('workpiece_name', 'Unknown')} (置信度: {final_result.get('confidence', 0):.3f}, 方法数: {final_result.get('method_count', 0)})")
                return final_result
            else:
                return {'success': False, 'error': '所有集成方法都未能识别出工件'}

        except Exception as e:
            self.logger.error(f"集成识别失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_additional_preprocessing_methods(self, image: np.ndarray) -> List[Tuple[str, np.ndarray]]:
        """获取额外的预处理方法"""
        methods = []

        try:
            # 1. 锐化滤波
            kernel_sharpen = np.array([[-1,-1,-1],
                                     [-1, 9,-1],
                                     [-1,-1,-1]])
            sharpened = cv2.filter2D(image, -1, kernel_sharpen)
            methods.append(('锐化滤波', sharpened))

            # 2. 边缘增强
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            edges = cv2.Canny(gray, 50, 150)
            edges_colored = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR) if len(image.shape) == 3 else edges
            methods.append(('边缘增强', edges_colored))

            # 3. 自适应阈值
            adaptive_thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            if len(image.shape) == 3:
                adaptive_thresh = cv2.cvtColor(adaptive_thresh, cv2.COLOR_GRAY2BGR)
            methods.append(('自适应阈值', adaptive_thresh))

            # 4. 伽马校正
            gamma = 1.5
            gamma_corrected = np.power(image / 255.0, gamma) * 255.0
            gamma_corrected = gamma_corrected.astype(np.uint8)
            methods.append(('伽马校正', gamma_corrected))

            # 5. 色彩空间转换 (HSV)
            if len(image.shape) == 3:
                hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
                # 增强饱和度
                hsv[:,:,1] = cv2.multiply(hsv[:,:,1], 1.2)
                enhanced_hsv = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
                methods.append(('HSV增强', enhanced_hsv))

        except Exception as e:
            self.logger.warning(f"预处理方法生成失败: {e}")

        return methods

    def _get_method_weight(self, method_name: str) -> float:
        """获取方法权重"""
        weights = {
            'enhanced': 2.0,
            '锐化滤波': 1.2,
            '边缘增强': 1.0,
            '自适应阈值': 1.1,
            '伽马校正': 1.3,
            'HSV增强': 1.2,
            '旋转_5': 1.1,
            '旋转_-5': 1.1,
            '旋转_10': 1.0,
            '旋转_-10': 1.0
        }
        return weights.get(method_name, 1.0)

    def _rotation_invariant_recognition(self, image: np.ndarray, bbox: Optional[Tuple[int, int, int, int]] = None) -> List[Dict]:
        """旋转不变性识别"""
        rotation_results = []
        angles = [5, -5, 10, -10]  # 小角度旋转

        h, w = image.shape[:2]
        center = (w // 2, h // 2)

        for angle in angles:
            try:
                # 旋转图像
                M = cv2.getRotationMatrix2D(center, angle, 1.0)
                rotated = cv2.warpAffine(image, M, (w, h))

                # 识别
                result = self.recognize_workpiece(rotated, bbox)
                if result['success']:
                    result['method'] = f'旋转_{angle}'
                    result['weight'] = self._get_method_weight(f'旋转_{angle}')
                    rotation_results.append(result)
                    self.logger.info(f"旋转{angle}度识别: {result['workpiece_name']} ({result['confidence']:.3f})")

            except Exception as e:
                self.logger.warning(f"旋转{angle}度识别失败: {e}")

        return rotation_results

    def _advanced_ensemble_fusion(self, results: List[Dict]) -> Dict:
        """高级集成融合"""
        if not results:
            return {'success': False, 'error': '没有结果可融合'}

        # 按工件名称分组
        workpiece_groups = {}
        for result in results:
            name = result['workpiece_name']
            if name not in workpiece_groups:
                workpiece_groups[name] = []
            workpiece_groups[name].append(result)

        # 计算每个工件的加权分数
        best_name = None
        best_score = 0
        best_group = None

        for name, group in workpiece_groups.items():
            # 加权置信度计算
            total_weighted_confidence = 0
            total_weight = 0

            for result in group:
                confidence = result['confidence']
                weight = result.get('weight', 1.0)

                total_weighted_confidence += confidence * weight
                total_weight += weight

            if total_weight > 0:
                avg_weighted_confidence = total_weighted_confidence / total_weight
            else:
                avg_weighted_confidence = 0

            # 多方法确认奖励 (更强的奖励)
            method_bonus = len(group) * 0.08

            # 方法多样性奖励
            unique_methods = len(set(r.get('method', 'unknown') for r in group))
            diversity_bonus = unique_methods * 0.03

            # 最终分数
            final_score = avg_weighted_confidence + method_bonus + diversity_bonus

            if final_score > best_score:
                best_score = final_score
                best_name = name
                best_group = group

        if best_name and best_group:
            # 选择该组中加权置信度最高的结果作为基础
            best_result = max(best_group, key=lambda x: x['confidence'] * x.get('weight', 1.0))

            # 更新最终结果
            final_result = best_result.copy()
            final_result['ensemble_score'] = best_score
            final_result['method_count'] = len(best_group)
            final_result['unique_methods'] = len(set(r.get('method', 'unknown') for r in best_group))
            final_result['all_methods'] = [r.get('method', 'unknown') for r in best_group]

            # 重新计算融合后的置信度
            total_weighted_confidence = sum(r['confidence'] * r.get('weight', 1.0) for r in best_group)
            total_weight = sum(r.get('weight', 1.0) for r in best_group)

            if total_weight > 0:
                final_result['confidence'] = min(total_weighted_confidence / total_weight, 1.0)

            return final_result

        return {'success': False, 'error': '高级融合失败'}

    def _detect_objects(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        自动检测图像中的对象

        Returns:
            检测到的对象边界框列表
        """
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 使用自适应阈值和轮廓检测
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY_INV, 11, 2)

            # 形态学操作
            kernel = np.ones((3, 3), np.uint8)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 过滤和排序轮廓
            valid_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 500:  # 最小面积阈值
                    valid_contours.append(contour)

            # 按面积排序，返回最大的几个
            valid_contours.sort(key=cv2.contourArea, reverse=True)

            # 转换为边界框
            bboxes = []
            for contour in valid_contours[:5]:  # 最多返回5个对象
                x, y, w, h = cv2.boundingRect(contour)
                bboxes.append((x, y, w, h))

            return bboxes

        except Exception as e:
            self.logger.error(f"对象检测失败: {e}")
            return []

    def save_workpiece_database(self):
        """保存工件数据库"""
        try:
            db_path = os.path.join(self.data_dir, "workpiece_database.json")
            with open(db_path, 'w', encoding='utf-8') as f:
                json.dump(self.workpiece_database, f, ensure_ascii=False, indent=2)
            self.logger.info(f"工件数据库已保存: {db_path}")
        except Exception as e:
            self.logger.error(f"保存工件数据库失败: {e}")

    def load_workpiece_database(self):
        """加载工件数据库"""
        try:
            db_path = os.path.join(self.data_dir, "workpiece_database.json")
            if os.path.exists(db_path):
                with open(db_path, 'r', encoding='utf-8') as f:
                    self.workpiece_database = json.load(f)
                self.logger.info(f"工件数据库已加载: {len(self.workpiece_database)} 个工件")
            else:
                self.workpiece_database = {}
                self.logger.info("创建新的工件数据库")
        except Exception as e:
            self.logger.error(f"加载工件数据库失败: {e}")
            self.workpiece_database = {}

    def get_workpiece_list(self) -> List[Dict]:
        """获取工件列表"""
        workpiece_list = []
        for workpiece_id, record in self.workpiece_database.items():
            workpiece_info = {
                'id': workpiece_id,
                'name': record['info'].get('name', 'Unknown'),
                'type': record['info'].get('type', 'Unknown'),
                'learn_count': record.get('learn_count', 1),
                'created_time': record.get('created_time', ''),
                'info': record['info']
            }
            workpiece_list.append(workpiece_info)
        return workpiece_list

    def delete_workpiece(self, workpiece_id: str) -> bool:
        """删除工件"""
        try:
            if workpiece_id not in self.workpiece_database:
                self.logger.error(f"工件ID不存在: {workpiece_id}")
                return False

            # 删除图像文件
            record = self.workpiece_database[workpiece_id]
            if 'image_path' in record and os.path.exists(record['image_path']):
                os.remove(record['image_path'])

            # 删除额外样本图像
            if 'additional_samples' in record:
                for sample in record['additional_samples']:
                    if 'image_path' in sample and os.path.exists(sample['image_path']):
                        os.remove(sample['image_path'])

            # 从数据库中删除
            del self.workpiece_database[workpiece_id]

            # 保存数据库
            self.save_workpiece_database()

            self.logger.info(f"工件已删除: {workpiece_id}")
            return True

        except Exception as e:
            self.logger.error(f"删除工件失败: {e}")
            return False

    def update_workpiece_info(self, workpiece_id: str, new_info: Dict) -> bool:
        """更新工件信息"""
        try:
            if workpiece_id not in self.workpiece_database:
                self.logger.error(f"工件ID不存在: {workpiece_id}")
                return False

            self.workpiece_database[workpiece_id]['info'].update(new_info)
            self.save_workpiece_database()

            self.logger.info(f"工件信息已更新: {workpiece_id}")
            return True

        except Exception as e:
            self.logger.error(f"更新工件信息失败: {e}")
            return False

    def get_model_info(self) -> Dict:
        """获取模型信息"""
        info = {
            'is_trained': self.is_trained,
            'workpiece_count': len(self.workpiece_database),
            'feature_type': self.feature_type,
            'model_exists': os.path.exists(os.path.join(self.models_dir, "workpiece_classifier.pkl"))
        }

        if info['model_exists']:
            try:
                model_path = os.path.join(self.models_dir, "workpiece_classifier.pkl")
                model_data = joblib.load(model_path)
                info['trained_time'] = model_data.get('trained_time', 'Unknown')
                info['trained_workpiece_count'] = model_data.get('workpiece_count', 0)
            except:
                pass

        return info


# 使用示例
if __name__ == "__main__":
    # 创建学习器实例
    learner = CustomWorkpieceLearner()

    # 示例：学习工件
    # test_image = cv2.imread("test_workpiece.jpg")
    # bbox = (100, 100, 200, 150)  # x, y, width, height
    # workpiece_info = {
    #     'name': '螺丝',
    #     'type': '紧固件',
    #     'specifications': 'M6x20',
    #     'material': '不锈钢',
    #     'supplier': 'ABC公司'
    # }
    # workpiece_id = learner.learn_workpiece(test_image, bbox, workpiece_info)

    # 训练模型
    # learner.train_model()

    # 识别工件
    # result = learner.recognize_workpiece(test_image)
    # print("识别结果:", result)

    print("自定义工件学习识别系统已初始化")
