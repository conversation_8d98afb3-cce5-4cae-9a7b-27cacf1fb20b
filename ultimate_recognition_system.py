#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极识别系统
集成所有增强技术，实现最高级别的识别准确率
"""

import sys
import os
import glob
import numpy as np
import cv2
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple

sys.path.append('.')
from custom_workpiece_learner import CustomWorkpieceLearner
from optimized_workpiece_learner import OptimizedWorkpieceLearner
from advanced_recognition_enhancer import AdvancedRecognitionEnhancer

class UltimateRecognitionSystem:
    """终极识别系统"""
    
    def __init__(self):
        """初始化终极识别系统"""
        self.logger = self._setup_logger()
        
        # 初始化各级别的识别器
        self.base_learner = CustomWorkpieceLearner()
        self.optimized_learner = OptimizedWorkpieceLearner()
        self.advanced_enhancer = AdvancedRecognitionEnhancer(self.optimized_learner)
        
        # 系统配置
        self.recognition_levels = {
            'basic': self.base_learner,
            'enhanced': self.optimized_learner,
            'advanced': self.advanced_enhancer
        }
        
        # 自适应策略
        self.adaptive_strategy = 'auto'  # auto, conservative, aggressive
        self.confidence_thresholds = {
            'basic': 0.6,
            'enhanced': 0.5,
            'advanced': 0.4
        }
        
        # 性能统计
        self.performance_stats = {
            'basic': {'success': 0, 'total': 0, 'avg_confidence': 0},
            'enhanced': {'success': 0, 'total': 0, 'avg_confidence': 0},
            'advanced': {'success': 0, 'total': 0, 'avg_confidence': 0}
        }
        
        self.is_advanced_ready = False
    
    def _setup_logger(self):
        """设置日志器"""
        import logging
        logger = logging.getLogger('UltimateRecognitionSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def initialize_advanced_system(self):
        """初始化高级系统"""
        try:
            self.logger.info("初始化高级识别系统...")
            
            # 确保基础模型已训练
            if not self.optimized_learner.is_trained:
                self.logger.info("训练优化模型...")
                success = self.optimized_learner.train_optimized_model()
                if not success:
                    self.logger.error("优化模型训练失败")
                    return False
            
            # 准备训练数据
            X, y, labels = self.optimized_learner._prepare_training_data()
            if not X or len(X) == 0:
                self.logger.error("没有训练数据")
                return False
            
            X = np.array(X)
            y = np.array(y)
            
            # 应用特征选择
            if (self.optimized_learner.use_feature_selection and 
                self.optimized_learner.selected_feature_indices is not None):
                if X.shape[1] >= len(self.optimized_learner.selected_feature_indices):
                    X = X[:, self.optimized_learner.selected_feature_indices]
            
            # 数据增强
            self.logger.info("执行高级数据增强...")
            augmented_X, augmented_y = self._perform_advanced_augmentation(X, y)
            
            # 训练高级集成模型
            self.logger.info("训练高级集成模型...")
            success = self.advanced_enhancer.train_advanced_ensemble(augmented_X, augmented_y, labels)
            
            if success:
                self.is_advanced_ready = True
                self.logger.info("高级识别系统初始化完成")
                return True
            else:
                self.logger.error("高级集成模型训练失败")
                return False
                
        except Exception as e:
            self.logger.error(f"高级系统初始化失败: {e}")
            return False
    
    def _perform_advanced_augmentation(self, X, y):
        """执行高级数据增强"""
        try:
            # 这里我们使用特征级别的增强
            # 在实际应用中，应该在图像级别进行增强然后提取特征
            
            augmented_X = X.copy()
            augmented_y = y.copy()
            
            # 简单的特征增强：添加噪声
            for i in range(len(X)):
                # 为每个样本生成2个增强版本
                for _ in range(2):
                    noise = np.random.normal(0, 0.01, X[i].shape)
                    augmented_sample = X[i] + noise
                    
                    augmented_X = np.vstack([augmented_X, augmented_sample])
                    augmented_y = np.append(augmented_y, y[i])
            
            self.logger.info(f"数据增强: {len(X)} → {len(augmented_X)} 样本")
            return augmented_X, augmented_y
            
        except Exception as e:
            self.logger.warning(f"数据增强失败: {e}")
            return X, y
    
    def ultimate_recognize(self, image: np.ndarray, strategy: str = 'auto') -> Dict:
        """终极识别方法"""
        try:
            self.logger.info("开始终极识别...")
            
            all_results = []
            
            # 1. 基础识别
            try:
                basic_result = self.base_learner.recognize_workpiece(image)
                if basic_result['success']:
                    basic_result['level'] = 'basic'
                    basic_result['method'] = 'basic_recognition'
                    all_results.append(basic_result)
                    self._update_stats('basic', basic_result)
            except Exception as e:
                self.logger.warning(f"基础识别失败: {e}")
            
            # 2. 增强识别
            try:
                enhanced_result = self.base_learner.enhanced_recognize_workpiece(image)
                if enhanced_result['success']:
                    enhanced_result['level'] = 'enhanced'
                    enhanced_result['method'] = 'enhanced_recognition'
                    all_results.append(enhanced_result)
                    self._update_stats('enhanced', enhanced_result)
            except Exception as e:
                self.logger.warning(f"增强识别失败: {e}")
            
            # 3. 高级识别
            if self.is_advanced_ready:
                try:
                    advanced_result = self.advanced_enhancer.advanced_predict(image)
                    if advanced_result['success']:
                        # 转换为标准格式
                        prediction = advanced_result['prediction']
                        confidence = advanced_result['confidence']
                        
                        # 获取工件名称
                        _, _, labels = self.optimized_learner._prepare_training_data()
                        workpiece_name = labels[prediction] if prediction < len(labels) else "Unknown"
                        
                        # 查找工件信息
                        workpiece_info = None
                        for record in self.optimized_learner.workpiece_database.values():
                            if record['info'].get('name') == workpiece_name:
                                workpiece_info = record['info'].copy()
                                break
                        
                        standard_result = {
                            'success': True,
                            'workpiece_name': workpiece_name,
                            'confidence': confidence,
                            'level': 'advanced',
                            'method': 'advanced_ensemble',
                            'center_x': image.shape[1] // 2,  # 默认中心
                            'center_y': image.shape[0] // 2,
                            'workpiece_info': workpiece_info,
                            'individual_predictions': advanced_result.get('individual_predictions', {}),
                            'calibrated': advanced_result.get('calibrated', False)
                        }
                        
                        all_results.append(standard_result)
                        self._update_stats('advanced', standard_result)
                        
                except Exception as e:
                    self.logger.warning(f"高级识别失败: {e}")
            
            # 4. 智能结果融合
            if all_results:
                final_result = self._intelligent_fusion(all_results, strategy)
                self.logger.info(f"终极识别完成: {final_result.get('workpiece_name', 'Unknown')} "
                                f"(置信度: {final_result.get('confidence', 0):.3f}, "
                                f"级别: {final_result.get('level', 'unknown')})")
                return final_result
            else:
                return {'success': False, 'error': '所有识别方法都失败了'}
                
        except Exception as e:
            self.logger.error(f"终极识别失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _intelligent_fusion(self, results: List[Dict], strategy: str) -> Dict:
        """智能结果融合"""
        if not results:
            return {'success': False, 'error': '没有结果可融合'}
        
        # 按工件名称分组
        workpiece_groups = {}
        for result in results:
            name = result['workpiece_name']
            if name not in workpiece_groups:
                workpiece_groups[name] = []
            workpiece_groups[name].append(result)
        
        # 计算每个工件的综合分数
        best_name = None
        best_score = 0
        best_result = None
        
        for name, group in workpiece_groups.items():
            # 级别权重
            level_weights = {
                'basic': 1.0,
                'enhanced': 1.5,
                'advanced': 2.0
            }
            
            # 计算加权分数
            total_score = 0
            total_weight = 0
            
            for result in group:
                level = result.get('level', 'basic')
                confidence = result.get('confidence', 0)
                weight = level_weights.get(level, 1.0)
                
                # 策略调整
                if strategy == 'conservative':
                    # 保守策略：更重视高级别的结果
                    weight *= 1.5 if level == 'advanced' else 1.0
                elif strategy == 'aggressive':
                    # 激进策略：平衡各级别
                    weight = 1.0
                
                total_score += confidence * weight
                total_weight += weight
            
            if total_weight > 0:
                avg_score = total_score / total_weight
                # 多方法确认奖励
                method_bonus = len(group) * 0.05
                final_score = avg_score + method_bonus
                
                if final_score > best_score:
                    best_score = final_score
                    best_name = name
                    # 选择该组中置信度最高的结果作为基础
                    best_result = max(group, key=lambda x: x['confidence'])
        
        if best_result:
            # 更新最终结果
            final_result = best_result.copy()
            final_result['fusion_score'] = best_score
            final_result['fusion_count'] = len(workpiece_groups[best_name])
            final_result['ultimate'] = True
            final_result['strategy'] = strategy
            
            return final_result
        
        return {'success': False, 'error': '融合失败'}
    
    def _update_stats(self, level: str, result: Dict):
        """更新性能统计"""
        if level in self.performance_stats:
            stats = self.performance_stats[level]
            stats['total'] += 1
            if result['success']:
                stats['success'] += 1
                confidence = result.get('confidence', 0)
                # 更新平均置信度
                current_avg = stats['avg_confidence']
                stats['avg_confidence'] = (current_avg * (stats['success'] - 1) + confidence) / stats['success']
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_status': {
                'basic_ready': True,
                'enhanced_ready': True,
                'advanced_ready': self.is_advanced_ready
            },
            'performance_stats': self.performance_stats.copy()
        }
        
        # 计算成功率
        for level, stats in report['performance_stats'].items():
            if stats['total'] > 0:
                stats['success_rate'] = stats['success'] / stats['total'] * 100
            else:
                stats['success_rate'] = 0
        
        return report
    
    def save_system_state(self, filepath: str):
        """保存系统状态"""
        try:
            state = {
                'performance_stats': self.performance_stats,
                'confidence_thresholds': self.confidence_thresholds,
                'is_advanced_ready': self.is_advanced_ready
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"系统状态已保存到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存系统状态失败: {e}")
    
    def load_system_state(self, filepath: str):
        """加载系统状态"""
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                
                self.performance_stats = state.get('performance_stats', self.performance_stats)
                self.confidence_thresholds = state.get('confidence_thresholds', self.confidence_thresholds)
                self.is_advanced_ready = state.get('is_advanced_ready', False)
                
                self.logger.info(f"系统状态已从 {filepath} 加载")
                return True
            
        except Exception as e:
            self.logger.error(f"加载系统状态失败: {e}")
        
        return False
